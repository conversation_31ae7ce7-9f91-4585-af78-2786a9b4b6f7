// 全局变量
let currentTaskId = null;
let progressInterval = null;
let currentAnalysisId = null;

// 结果筛选相关变量
let originalResults = []; // 保存原始结果用于筛选
let filteredResults = []; // 筛选后的结果
let currentDisplayedResults = []; // 当前显示的结果（用于导出）
let resultsFilters = {
    keywords: [],
    companies: [],
    minCount: 1,
    hideZero: true
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 绑定事件监听器
    bindEventListeners();
    
    // 初始化界面状态
    updateDataSourceVisibility();
    
    // 添加初始日志
    addLog('系统已就绪，请选择操作...', 'info');
}

function bindEventListeners() {
    // 数据源选择变化
    document.querySelectorAll('input[name="dataSource"]').forEach(radio => {
        radio.addEventListener('change', updateDataSourceVisibility);
    });
    
    // 按钮事件
    document.getElementById('startAnalysisBtn').addEventListener('click', startAnalysis);
    document.getElementById('stopAnalysisBtn').addEventListener('click', stopAnalysis);
    document.getElementById('keywordOnlyBtn').addEventListener('click', keywordOnlyAnalysis);
    document.getElementById('importTxtBtn').addEventListener('click', importTxtFiles);
    document.getElementById('clearLogBtn').addEventListener('click', clearLog);
    document.getElementById('exportResultsBtn').addEventListener('click', exportResults);
    document.getElementById('showSummaryBtn').addEventListener('click', showSummary);
    document.getElementById('cleanDuplicatesBtn').addEventListener('click', cleanDuplicates);
    document.getElementById('aiAnalysisBtn').addEventListener('click', openAiAnalysisModal);
    document.getElementById('startAiAnalysisBtn').addEventListener('click', startAiAnalysis);
    document.getElementById('clearAiFormBtn').addEventListener('click', clearAiForm);
    document.getElementById('debugDatabaseBtn').addEventListener('click', debugDatabase);


}

function updateDataSourceVisibility() {
    const isOnline = document.getElementById('onlineSource').checked;
    const searchKeywordGroup = document.getElementById('searchKeywordGroup');
    const dateRangeGroup = document.getElementById('dateRangeGroup');
    
    if (isOnline) {
        searchKeywordGroup.style.display = 'block';
        dateRangeGroup.style.display = 'block';
    } else {
        searchKeywordGroup.style.display = 'none';
        dateRangeGroup.style.display = 'none';
    }
}

// 全局强制关闭所有弹窗的函数
function forceCloseAllModals() {
    try {
        // 关闭所有Bootstrap模态框
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');
            modal.removeAttribute('aria-modal');
            modal.removeAttribute('role');
        });

        // 清理body状态
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // 移除所有backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        console.log('强制关闭所有弹窗完成');
    } catch (error) {
        console.error('强制关闭弹窗失败:', error);
    }
}

// 添加键盘快捷键 Esc 强制关闭弹窗
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        forceCloseAllModals();
    }
});

function addLog(message, type = 'info') {
    const logOutput = document.getElementById('logOutput');
    const timestamp = new Date().toLocaleString('zh-CN');
    
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `
        <span class="timestamp">[${timestamp}]</span>
        <span class="message">${message}</span>
    `;
    
    logOutput.appendChild(logEntry);
    logOutput.scrollTop = logOutput.scrollHeight;
}

function clearLog() {
    const logOutput = document.getElementById('logOutput');
    logOutput.innerHTML = '';
    addLog('日志已清空', 'info');
}

function showLoading(text = '处理中...') {
    document.getElementById('loadingText').textContent = text;
    const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
    modal.show();
}

function hideLoading() {
    try {
        const loadingModal = document.getElementById('loadingModal');

        // 方法1: 使用Bootstrap Modal API
        const modal = bootstrap.Modal.getInstance(loadingModal);
        if (modal) {
            modal.hide();
        }

        // 方法2: 立即强制关闭
        if (loadingModal) {
            loadingModal.style.display = 'none';
            loadingModal.classList.remove('show');
            loadingModal.setAttribute('aria-hidden', 'true');
            loadingModal.removeAttribute('aria-modal');
            loadingModal.removeAttribute('role');
        }

        // 立即清理body状态
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // 立即移除所有backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // 方法3: 延迟再次强制关闭（确保一定关闭）
        setTimeout(() => {
            if (loadingModal) {
                loadingModal.style.display = 'none';
                loadingModal.classList.remove('show');
                loadingModal.setAttribute('aria-hidden', 'true');
                loadingModal.removeAttribute('aria-modal');
                loadingModal.removeAttribute('role');
            }

            // 再次清理body状态
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // 再次移除所有backdrop
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
        }, 50);

        // 方法4: 最后一次强制清理
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 200);

    } catch (error) {
        console.error('关闭加载弹窗失败:', error);
        // 即使出错也要强制清理
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }
}

function updateProgress(progress, message, status = 'running') {
    const progressCard = document.getElementById('progressCard');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const taskStatus = document.getElementById('taskStatus');
    
    progressCard.style.display = 'block';
    progressBar.style.width = `${progress}%`;
    progressBar.textContent = `${progress}%`;
    progressText.textContent = message;
    
    // 更新状态徽章
    let badgeClass = 'bg-secondary';
    let statusText = '等待中';
    
    switch (status) {
        case 'running':
            badgeClass = 'bg-primary';
            statusText = '运行中';
            break;
        case 'completed':
            badgeClass = 'bg-success';
            statusText = '已完成';
            break;
        case 'error':
            badgeClass = 'bg-danger';
            statusText = '错误';
            break;
        case 'stopped':
            badgeClass = 'bg-warning';
            statusText = '已停止';
            break;
    }
    
    taskStatus.innerHTML = `<span class="badge ${badgeClass}">${statusText}</span>`;
}

function startAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();
    const searchKeyword = document.getElementById('searchKeyword').value.trim();
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const useOnline = document.getElementById('onlineSource').checked;
    const relatedParties = document.getElementById('relatedParties').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入关键词');
        return;
    }

    // 禁用开始按钮，启用停止按钮
    document.getElementById('startAnalysisBtn').disabled = true;
    document.getElementById('stopAnalysisBtn').disabled = false;

    let logMessage = '开始分析任务...';
    if (relatedParties) {
        logMessage += ' (包含关联方分析)';
    }

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        search_keyword: searchKeyword,
        start_date: startDate,
        end_date: endDate,
        use_online: useOnline,
        related_parties: relatedParties
    };



    addLog(logMessage, 'info');
    
    fetch('/api/start_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentTaskId = data.task_id;
            addLog(`任务已启动，任务ID: ${currentTaskId}`, 'success');
            startProgressMonitoring();
        } else {
            addLog(`启动失败: ${data.message}`, 'error');
            resetButtons();
        }
    })
    .catch(error => {
        addLog(`请求失败: ${error.message}`, 'error');
        resetButtons();
    });
}

function stopAnalysis() {
    if (!currentTaskId) {
        return;
    }
    
    fetch(`/api/stop_task/${currentTaskId}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLog('任务已停止', 'warning');
        } else {
            addLog(`停止失败: ${data.message}`, 'error');
        }
        stopProgressMonitoring();
        resetButtons();
    })
    .catch(error => {
        addLog(`停止请求失败: ${error.message}`, 'error');
    });
}

function keywordOnlyAnalysis() {
    const stockCodes = document.getElementById('stockCodes').value.trim();
    const keywords = document.getElementById('keywords').value.trim();
    const relatedParties = document.getElementById('relatedParties').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入关键词');
        return;
    }

    showLoading('正在分析关键词...');
    let logMessage = '开始关键词分析...';
    if (relatedParties) {
        logMessage += ' (包含关联方分析)';
    }
    addLog(logMessage, 'info');

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        related_parties: relatedParties
    };

    fetch('/api/keyword_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 确保关闭加载弹窗
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            currentAnalysisId = data.analysis_id;
            currentTaskId = null; // 清除任务ID，使用分析ID
            addLog(`关键词分析完成: ${data.message}`, 'success');
            displayResults(data.data);

            // 显示关联方分析结果
            if (data.related_party_analysis && Object.keys(data.related_party_analysis).length > 0) {
                displayRelatedPartyAnalysis(data.related_party_analysis);
                addLog('🤝 关联方协同创新分析完成', 'success');
            }

            // 启用导出和摘要按钮
            document.getElementById('exportResultsBtn').disabled = false;
            document.getElementById('showSummaryBtn').disabled = false;

            addLog('💡 提示：现在可以点击数字旁边的眼睛图标查看关键词上下文', 'info');
        } else {
            addLog(`分析失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('关键词分析请求失败:', error);
        // 确保在错误情况下也关闭加载弹窗
        hideLoading();
        addLog(`分析请求失败: ${error.message}`, 'error');

        // 强制关闭弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);
    });
}

function importTxtFiles() {
    showLoading('正在导入TXT文件...');
    addLog('开始导入TXT文件到数据库...', 'info');

    fetch('/api/import_txt', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({txt_dir: 'txt'})
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭加载弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            addLog(`导入完成: ${data.message}`, 'success');

            // 显示详细统计信息
            if (data.imported > 0) {
                addLog(`✅ 新增导入: ${data.imported} 个文件`, 'success');
            }
            if (data.skipped > 0) {
                addLog(`⏭️ 跳过重复: ${data.skipped} 个文件`, 'info');
            }
            if (data.errors > 0) {
                addLog(`❌ 导入失败: ${data.errors} 个文件`, 'warning');
            }

            addLog(`📊 总计处理: ${data.total} 个文件`, 'info');

            // 如果有新导入的文件，建议刷新
            if (data.imported > 0) {
                addLog('💡 提示：有新文件导入，建议刷新页面查看最新数据', 'info');
            }
        } else {
            addLog(`导入失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭加载弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);

        addLog(`导入请求失败: ${error.message}`, 'error');
    });
}

function startProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    progressInterval = setInterval(() => {
        if (!currentTaskId) {
            stopProgressMonitoring();
            return;
        }
        
        fetch(`/api/task_status/${currentTaskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskData = data.data;
                updateProgress(taskData.progress, taskData.message, taskData.status);
                
                if (taskData.status === 'completed') {
                    addLog('任务完成！', 'success');
                    stopProgressMonitoring();
                    resetButtons();
                    loadTaskResults();
                } else if (taskData.status === 'error') {
                    addLog(`任务失败: ${taskData.message}`, 'error');
                    stopProgressMonitoring();
                    resetButtons();
                } else if (taskData.status === 'stopped') {
                    addLog('任务已停止', 'warning');
                    stopProgressMonitoring();
                    resetButtons();
                }
            }
        })
        .catch(error => {
            console.error('获取任务状态失败:', error);
        });
    }, 2000); // 每2秒检查一次
}

function stopProgressMonitoring() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

function resetButtons() {
    document.getElementById('startAnalysisBtn').disabled = false;
    document.getElementById('stopAnalysisBtn').disabled = true;
}

function loadTaskResults() {
    if (!currentTaskId) {
        return;
    }

    fetch(`/api/analysis_results/${currentTaskId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 设置分析ID，使上下文查看功能可用
            currentAnalysisId = currentTaskId; // 对于"开始分析"，使用task_id作为analysis_id
            displayResults(data.data);

            // 启用导出和摘要按钮
            document.getElementById('exportResultsBtn').disabled = false;
            document.getElementById('showSummaryBtn').disabled = false;

            addLog('💡 提示：现在可以点击数字旁边的眼睛图标查看关键词上下文', 'info');
        } else {
            addLog(`获取结果失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        addLog(`获取结果请求失败: ${error.message}`, 'error');
    });
}

function displayResults(results) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');

    // 保存完整数据到全局变量
    fullResultsData = results;
    originalResults = results; // 保存原始结果用于筛选

    // 初始化筛选选项
    initializeResultsFilters(results);

    // 设置当前显示的结果
    currentDisplayedResults = results;

    // 更新导出按钮状态
    updateExportButtonText();

    // 调试信息
    console.log('displayResults 收到的数据:', results);
    console.log('数据类型:', typeof results);
    console.log('是否为数组:', Array.isArray(results));

    if (!results) {
        fullResultsData = null;
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    // 检查数据格式
    if (Array.isArray(results) && results.length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    if (typeof results === 'object' && !Array.isArray(results) && Object.keys(results).length === 0) {
        resultsContent.innerHTML = '<p class="text-muted">没有分析结果</p>';
        resultsCard.style.display = 'block';
        return;
    }

    let html = '';

    // 如果是数组格式（从数据库获取的结果）
    if (Array.isArray(results)) {
        console.log('使用数组格式处理，数据长度:', results.length);
        if (results.length > 0) {
            console.log('第一条数据示例:', results[0]);
        }
        html = generateTableFromArray(results);
    } else {
        // 如果是对象格式（直接分析的结果）
        console.log('使用对象格式处理，键数量:', Object.keys(results).length);
        if (Object.keys(results).length > 0) {
            console.log('第一个股票数据示例:', Object.entries(results)[0]);
        }
        html = generateTableFromObject(results);
    }

    // 添加分页功能
    const paginatedHtml = addPaginationToResults(html);
    resultsContent.innerHTML = paginatedHtml;
    resultsCard.style.display = 'block';

    // 启用导出和摘要按钮
    const exportBtn = document.getElementById('exportResultsBtn');
    const summaryBtn = document.getElementById('showSummaryBtn');

    if (exportBtn) exportBtn.disabled = false;
    if (summaryBtn) summaryBtn.disabled = false;

    // 添加成功日志
    addLog('结果显示完成，可以查看分析结果', 'success');
}

function generateTableFromObject(results) {
    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th style="min-width: 100px;">股票代码</th><th style="min-width: 200px;">文件名</th>';

    // 获取所有关键词
    const allKeywords = new Set();
    Object.values(results).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.keys(fileData).forEach(keyword => allKeywords.add(keyword));
        });
    });

    // 按关键词长度排序，确保显示一致性
    const sortedKeywords = Array.from(allKeywords).sort();

    sortedKeywords.forEach(keyword => {
        html += `<th class="keyword-cell text-center" style="min-width: 80px; font-weight: bold; background-color: #f8f9fa;">${keyword}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 生成数据行
    Object.entries(results).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            html += `<tr><td><strong>${stockCode}</strong></td><td title="${fileName}">${fileName}</td>`;
            sortedKeywords.forEach(keyword => {
                const count = fileData[keyword] || 0;
                const cellClass = getCountClass(count);
                let cellContent = count;

                // 如果有出现次数，添加查看上下文按钮
                if (count > 0 && (currentAnalysisId || currentTaskId)) {
                    cellContent = `
                        <div class="d-flex align-items-center justify-content-center">
                            <span class="me-2">${count}</span>
                            <button class="btn btn-sm btn-outline-info" onclick="showKeywordContext('${keyword}', '${stockCode}', '${fileName}')" title="查看上下文">
                                <i class="bi bi-eye" style="font-size: 0.8em;"></i>
                            </button>
                        </div>
                    `;
                }

                html += `<td class="count-cell text-center ${cellClass}" title="${keyword}: ${count}次">${cellContent}</td>`;
            });
            html += '</tr>';
        });
    });
    
    html += '</tbody></table></div>';
    return html;
}

function generateTableFromArray(results) {
    if (results.length === 0) {
        return '<p class="text-muted">没有分析结果</p>';
    }

    // 前端去重：基于 stock_code + file_name + keyword 的组合
    const uniqueResults = [];
    const seen = new Set();

    results.forEach(result => {
        const key = `${result.stock_code}_${result.file_name}_${result.keyword}`;
        if (!seen.has(key)) {
            seen.add(key);
            uniqueResults.push(result);
        }
    });

    console.log(`原始数据: ${results.length} 条，去重后: ${uniqueResults.length} 条`);

    let html = '<div class="table-responsive"><table class="table table-striped results-table">';
    html += '<thead><tr><th>股票代码</th><th>公司名称</th><th>文件名</th><th>关键词</th><th>出现次数</th><th>分析日期</th></tr></thead><tbody>';

    uniqueResults.forEach(result => {
        const count = result.count || 0;
        const cellClass = getCountClass(count);
        const analysisDate = result.analysis_date ? new Date(result.analysis_date).toLocaleString('zh-CN') : 'Invalid Date';

        html += `
            <tr>
                <td><strong>${result.stock_code}</strong></td>
                <td>${result.company_name || ''}</td>
                <td title="${result.file_name || ''}">${(result.file_name || '').substring(0, 30)}${(result.file_name || '').length > 30 ? '...' : ''}</td>
                <td class="keyword-cell">${result.keyword}</td>
                <td class="count-cell ${cellClass}">${count}</td>
                <td>${analysisDate}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    return html;
}

// 筛选功能实现
function initializeResultsFilters(results) {
    if (!results) return;

    const keywordFilter = document.getElementById('resultsKeywordFilter');
    const companyFilter = document.getElementById('resultsCompanyFilter');

    if (!keywordFilter || !companyFilter) return;

    // 清空现有选项
    keywordFilter.innerHTML = '';
    companyFilter.innerHTML = '';

    const keywords = new Set();
    const companies = new Set();

    if (Array.isArray(results)) {
        // 数组格式（从数据库获取的结果）
        results.forEach(result => {
            if (result.keyword) keywords.add(result.keyword);
            if (result.company_name) companies.add(result.company_name);
            if (result.stock_code) companies.add(result.stock_code);
        });
    } else {
        // 对象格式（直接分析的结果）
        Object.entries(results).forEach(([stockCode, stockData]) => {
            companies.add(stockCode);
            Object.values(stockData).forEach(fileData => {
                Object.keys(fileData).forEach(keyword => keywords.add(keyword));
            });
        });
    }

    // 填充关键词选项
    Array.from(keywords).sort().forEach(keyword => {
        const option = document.createElement('option');
        option.value = keyword;
        option.textContent = keyword;
        keywordFilter.appendChild(option);
    });

    // 填充公司选项
    Array.from(companies).sort().forEach(company => {
        const option = document.createElement('option');
        option.value = company;
        option.textContent = company;
        companyFilter.appendChild(option);
    });
}

function applyResultsFilters() {
    if (!originalResults) {
        console.warn('没有原始结果数据可筛选');
        return;
    }

    // 获取筛选条件
    const keywordFilter = document.getElementById('resultsKeywordFilter');
    const companyFilter = document.getElementById('resultsCompanyFilter');
    const minCountInput = document.getElementById('minOccurrenceCount');
    const hideZeroCheckbox = document.getElementById('hideZeroOccurrences');

    const selectedKeywords = Array.from(keywordFilter.selectedOptions).map(option => option.value);
    const selectedCompanies = Array.from(companyFilter.selectedOptions).map(option => option.value);
    const minCount = parseInt(minCountInput.value) || 0;
    const hideZero = hideZeroCheckbox.checked;

    // 更新筛选状态
    resultsFilters = {
        keywords: selectedKeywords,
        companies: selectedCompanies,
        minCount: minCount,
        hideZero: hideZero
    };

    // 应用筛选
    let filteredData;

    if (Array.isArray(originalResults)) {
        filteredData = filterArrayResults(originalResults, resultsFilters);
    } else {
        filteredData = filterObjectResults(originalResults, resultsFilters);
    }

    // 显示筛选后的结果
    displayFilteredResults(filteredData);

    // 更新筛选状态显示
    updateFilterStatus();
}

function filterArrayResults(results, filters) {
    return results.filter(result => {
        // 关键词筛选
        if (filters.keywords.length > 0 && !filters.keywords.includes(result.keyword)) {
            return false;
        }

        // 公司筛选
        if (filters.companies.length > 0) {
            const matchCompany = filters.companies.includes(result.stock_code) ||
                               filters.companies.includes(result.company_name);
            if (!matchCompany) return false;
        }

        // 出现次数筛选
        const count = result.count || 0;
        if (count < filters.minCount) return false;

        // 隐藏0次记录
        if (filters.hideZero && count === 0) return false;

        return true;
    });
}

function filterObjectResults(results, filters) {
    const filteredResults = {};

    Object.entries(results).forEach(([stockCode, stockData]) => {
        // 公司筛选
        if (filters.companies.length > 0 && !filters.companies.includes(stockCode)) {
            return;
        }

        const filteredStockData = {};

        Object.entries(stockData).forEach(([fileName, fileData]) => {
            const filteredFileData = {};

            Object.entries(fileData).forEach(([keyword, count]) => {
                // 关键词筛选
                if (filters.keywords.length > 0 && !filters.keywords.includes(keyword)) {
                    return;
                }

                // 出现次数筛选
                if (count < filters.minCount) return;

                // 隐藏0次记录
                if (filters.hideZero && count === 0) return;

                filteredFileData[keyword] = count;
            });

            // 只有当文件有符合条件的关键词时才包含
            if (Object.keys(filteredFileData).length > 0) {
                filteredStockData[fileName] = filteredFileData;
            }
        });

        // 只有当股票有符合条件的文件时才包含
        if (Object.keys(filteredStockData).length > 0) {
            filteredResults[stockCode] = filteredStockData;
        }
    });

    return filteredResults;
}

// 获取计数值对应的CSS类
function getCountClass(count) {
    if (count === 0) return 'count-zero';
    if (count >= 10) return 'count-high';
    if (count >= 5) return 'count-medium';
    return '';
}

function displayFilteredResults(filteredData) {
    const resultsContent = document.getElementById('resultsContent');

    // 保存当前显示的结果用于导出
    currentDisplayedResults = filteredData;

    if (!filteredData ||
        (Array.isArray(filteredData) && filteredData.length === 0) ||
        (typeof filteredData === 'object' && Object.keys(filteredData).length === 0)) {
        resultsContent.innerHTML = '<p class="text-muted">没有符合筛选条件的结果</p>';
        currentDisplayedResults = [];
        return;
    }

    let html = '';

    if (Array.isArray(filteredData)) {
        html = generateTableFromArray(filteredData);
    } else {
        html = generateTableFromObject(filteredData);
    }

    resultsContent.innerHTML = html;

    // 显示筛选统计信息
    displayFilterStatistics(filteredData);

    // 更新导出按钮文本
    updateExportButtonText();
}

function displayFilterStatistics(filteredData) {
    const statusDiv = document.getElementById('resultsFilterStatus');
    if (!statusDiv) return;

    let totalRecords = 0;
    let totalKeywords = new Set();
    let totalCompanies = new Set();
    let highCountRecords = 0;

    if (Array.isArray(filteredData)) {
        totalRecords = filteredData.length;
        filteredData.forEach(record => {
            totalKeywords.add(record.keyword);
            totalCompanies.add(record.stock_code);
            if ((record.count || 0) >= 5) highCountRecords++;
        });
    } else {
        Object.entries(filteredData).forEach(([stockCode, stockData]) => {
            totalCompanies.add(stockCode);
            Object.entries(stockData).forEach(([fileName, fileData]) => {
                Object.entries(fileData).forEach(([keyword, count]) => {
                    totalKeywords.add(keyword);
                    totalRecords++;
                    if (count >= 5) highCountRecords++;
                });
            });
        });
    }

    const statsHtml = `
        <div class="d-flex flex-wrap gap-3 align-items-center">
            <span><i class="bi bi-table"></i> 记录数: <strong>${totalRecords}</strong></span>
            <span><i class="bi bi-tags"></i> 关键词: <strong>${totalKeywords.size}</strong></span>
            <span><i class="bi bi-building"></i> 公司: <strong>${totalCompanies.size}</strong></span>
            <span><i class="bi bi-star"></i> 高频(≥5): <strong>${highCountRecords}</strong></span>
        </div>
    `;

    // 如果有筛选条件，显示筛选信息和统计
    const activeFilters = [];
    if (resultsFilters.keywords.length > 0) {
        activeFilters.push(`关键词: ${resultsFilters.keywords.join(', ')}`);
    }
    if (resultsFilters.companies.length > 0) {
        activeFilters.push(`公司: ${resultsFilters.companies.join(', ')}`);
    }
    if (resultsFilters.minCount > 1) {
        activeFilters.push(`最小次数: ${resultsFilters.minCount}`);
    }
    if (resultsFilters.hideZero) {
        activeFilters.push('隐藏0次记录');
    }

    if (activeFilters.length > 0) {
        statusDiv.innerHTML = `
            <div class="mb-2">
                <i class="bi bi-funnel-fill text-primary"></i>
                <strong>已应用筛选:</strong> ${activeFilters.join(' | ')}
            </div>
            ${statsHtml}
        `;
    } else {
        statusDiv.innerHTML = `
            <div class="mb-2">
                <i class="bi bi-info-circle text-success"></i>
                <strong>显示全部结果</strong>
            </div>
            ${statsHtml}
        `;
    }
}

function clearResultsFilters() {
    // 重置筛选控件
    document.getElementById('resultsKeywordFilter').selectedIndex = -1;
    document.getElementById('resultsCompanyFilter').selectedIndex = -1;
    document.getElementById('minOccurrenceCount').value = 1;
    document.getElementById('hideZeroOccurrences').checked = true;

    // 重置筛选状态
    resultsFilters = {
        keywords: [],
        companies: [],
        minCount: 1,
        hideZero: true
    };

    // 显示原始结果
    if (originalResults) {
        displayFilteredResults(originalResults);
    }

    // 清除筛选状态显示
    updateFilterStatus();

    // 更新导出按钮
    updateExportButtonText();
}

function updateFilterStatus() {
    // 这个函数现在由displayFilterStatistics处理
    // 保留空函数以避免调用错误
}

// 快速筛选功能
function quickFilter(type) {
    const minCountInput = document.getElementById('minOccurrenceCount');
    const hideZeroCheckbox = document.getElementById('hideZeroOccurrences');
    const keywordFilter = document.getElementById('resultsKeywordFilter');
    const companyFilter = document.getElementById('resultsCompanyFilter');

    switch (type) {
        case 'hideZero':
            hideZeroCheckbox.checked = true;
            minCountInput.value = 1;
            break;

        case 'showAll':
            hideZeroCheckbox.checked = false;
            minCountInput.value = 0;
            keywordFilter.selectedIndex = -1;
            companyFilter.selectedIndex = -1;
            break;

        case 'highCount':
            hideZeroCheckbox.checked = true;
            minCountInput.value = 5;
            break;

        case 'lowCount':
            hideZeroCheckbox.checked = true;
            minCountInput.value = 1;
            // 设置最大值为4（需要在应用筛选时处理）
            break;
    }

    // 自动应用筛选
    applyResultsFilters();
}

// 增强的筛选函数，支持范围筛选
function filterArrayResults(results, filters) {
    return results.filter(result => {
        // 关键词筛选
        if (filters.keywords.length > 0 && !filters.keywords.includes(result.keyword)) {
            return false;
        }

        // 公司筛选
        if (filters.companies.length > 0) {
            const matchCompany = filters.companies.includes(result.stock_code) ||
                               filters.companies.includes(result.company_name);
            if (!matchCompany) return false;
        }

        // 出现次数筛选
        const count = result.count || 0;

        // 特殊处理低频词筛选（1-4次）
        const minCountInput = document.getElementById('minOccurrenceCount');
        if (minCountInput && minCountInput.value == 1 &&
            document.querySelector('.btn-outline-warning:focus')?.textContent.includes('低频词')) {
            if (count < 1 || count > 4) return false;
        } else {
            if (count < filters.minCount) return false;
        }

        // 隐藏0次记录
        if (filters.hideZero && count === 0) return false;

        return true;
    });
}

function exportResults(format = 'excel') {
    // 检查是否有筛选后的结果可导出
    if (currentDisplayedResults &&
        ((Array.isArray(currentDisplayedResults) && currentDisplayedResults.length > 0) ||
         (typeof currentDisplayedResults === 'object' && Object.keys(currentDisplayedResults).length > 0))) {

        // 导出筛选后的结果
        exportFilteredResults(format);
        return;
    }

    // 如果没有筛选结果，使用原有的导出方式
    if (!currentTaskId && !currentAnalysisId) {
        alert('没有可导出的结果');
        return;
    }

    if (format === 'excel') {
        const taskId = currentTaskId || currentAnalysisId;
        window.open(`/api/export_results/${taskId}`, '_blank');
        addLog('正在导出Excel文件...', 'info');
    } else if (format === 'csv') {
        const taskId = currentTaskId || currentAnalysisId;
        window.open(`/api/export_results/${taskId}?format=csv`, '_blank');
        addLog('正在导出CSV文件...', 'info');
    } else if (format === 'summary') {
        exportSummaryReport();
    }
}

function exportFilteredResults(format = 'excel') {
    if (!currentDisplayedResults) {
        alert('没有可导出的筛选结果');
        return;
    }

    // 显示导出进度
    const exportBtn = document.getElementById('exportResultsBtn');
    const originalText = exportBtn.innerHTML;
    const originalClass = exportBtn.className;

    exportBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 导出中...';
    exportBtn.className = 'btn btn-sm btn-secondary';
    exportBtn.disabled = true;

    // 准备导出数据
    const exportData = prepareExportData(currentDisplayedResults);

    addLog('开始导出筛选结果...', 'info');

    // 发送到后端进行Excel生成
    fetch('/api/export_filtered_results', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            data: exportData,
            filters: resultsFilters,
            format: format,
            timestamp: new Date().toISOString()
        })
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('导出失败');
    })
    .then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        const filterInfo = getFilterInfo();
        const extension = format === 'csv' ? 'csv' : 'xlsx';
        a.download = `筛选结果_${filterInfo}_${timestamp}.${extension}`;

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        addLog('筛选结果导出成功', 'success');
    })
    .catch(error => {
        console.error('导出失败:', error);
        addLog(`导出失败: ${error.message}`, 'error');
        alert(`导出失败: ${error.message}`);
    })
    .finally(() => {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.className = originalClass;
        exportBtn.disabled = false;
    });
}

function prepareExportData(results) {
    if (Array.isArray(results)) {
        // 数组格式：转换为标准格式
        return results.map(item => ({
            stock_code: item.stock_code || '',
            company_name: item.company_name || '',
            file_name: item.file_name || '',
            keyword: item.keyword || '',
            count: item.count || 0,
            analysis_date: item.analysis_date || ''
        }));
    } else {
        // 对象格式：转换为数组格式
        const exportArray = [];
        Object.entries(results).forEach(([stockCode, stockData]) => {
            Object.entries(stockData).forEach(([fileName, fileData]) => {
                Object.entries(fileData).forEach(([keyword, count]) => {
                    exportArray.push({
                        stock_code: stockCode,
                        company_name: '', // 对象格式中通常没有公司名称
                        file_name: fileName,
                        keyword: keyword,
                        count: count,
                        analysis_date: new Date().toISOString()
                    });
                });
            });
        });
        return exportArray;
    }
}

function getFilterInfo() {
    const filterParts = [];

    if (resultsFilters.keywords.length > 0) {
        filterParts.push(`关键词${resultsFilters.keywords.length}个`);
    }

    if (resultsFilters.companies.length > 0) {
        filterParts.push(`公司${resultsFilters.companies.length}个`);
    }

    if (resultsFilters.minCount > 1) {
        filterParts.push(`最小${resultsFilters.minCount}次`);
    }

    if (resultsFilters.hideZero) {
        filterParts.push('隐藏0次');
    }

    return filterParts.length > 0 ? filterParts.join('_') : '全部';
}

function updateExportButtonText() {
    const exportBtn = document.getElementById('exportResultsBtn');
    if (!exportBtn) return;

    // 检查是否有筛选条件
    const hasFilters = resultsFilters.keywords.length > 0 ||
                      resultsFilters.companies.length > 0 ||
                      resultsFilters.minCount > 1 ||
                      resultsFilters.hideZero;

    if (hasFilters && currentDisplayedResults) {
        // 计算筛选后的记录数
        let recordCount = 0;
        if (Array.isArray(currentDisplayedResults)) {
            recordCount = currentDisplayedResults.length;
        } else if (typeof currentDisplayedResults === 'object') {
            Object.values(currentDisplayedResults).forEach(stockData => {
                Object.values(stockData).forEach(fileData => {
                    recordCount += Object.keys(fileData).length;
                });
            });
        }

        exportBtn.innerHTML = `<i class="bi bi-download"></i> 导出筛选结果 (${recordCount}条)`;
        exportBtn.className = 'btn btn-sm btn-warning'; // 使用警告色表示这是筛选结果
        exportBtn.title = '导出当前筛选后的结果';
    } else {
        exportBtn.innerHTML = '<i class="bi bi-download"></i> 导出Excel';
        exportBtn.className = 'btn btn-sm btn-success'; // 恢复原来的颜色
        exportBtn.title = '导出完整结果';
    }
}

// 全局变量存储完整的结果数据
let fullResultsData = null;

// 统计摘要分页相关变量
let currentKeywordPage = 1;
let keywordPageSize = 10;
let currentStockPage = 1;
let stockPageSize = 10;
let currentSummaryStats = null;
let currentKeywordHeaders = null;

function showSummary() {
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));

    // 检查是否有完整的结果数据
    if (!fullResultsData) {
        document.getElementById('summaryContent').innerHTML = '<p class="text-muted">没有可统计的数据</p>';
        modal.show();
        return;
    }

    // 重置分页状态
    currentKeywordPage = 1;
    currentStockPage = 1;
    keywordPageSize = 10;
    stockPageSize = 10;

    // 使用完整数据生成统计
    const summaryHtml = generateSummaryFromFullData();
    document.getElementById('summaryContent').innerHTML = summaryHtml;
    modal.show();
}

function generateSummaryContent() {
    // 从当前显示的表格中提取数据
    const table = document.querySelector('.results-table');
    if (!table) return '<p class="text-muted">没有数据可统计</p>';

    const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
    const rows = Array.from(table.querySelectorAll('tbody tr'));

    // 提取关键词列（跳过股票代码和文件名列）
    const keywordHeaders = headers.slice(2);

    // 统计数据
    const stats = {
        totalFiles: rows.length,
        totalStocks: new Set(rows.map(row => row.cells[0].textContent.trim())).size,
        keywordStats: {},
        stockStats: {},
        fileStats: []
    };

    // 初始化关键词统计
    keywordHeaders.forEach(keyword => {
        stats.keywordStats[keyword] = {
            totalCount: 0,
            filesWithKeyword: 0,
            maxCount: 0,
            minCount: Infinity
        };
    });

    // 处理每一行数据
    rows.forEach(row => {
        const stockCode = row.cells[0].textContent.trim();
        const fileName = row.cells[1].textContent.trim();
        const fileStats = { stockCode, fileName, keywords: {} };

        // 初始化股票统计
        if (!stats.stockStats[stockCode]) {
            stats.stockStats[stockCode] = {
                fileCount: 0,
                totalKeywords: 0,
                keywords: {}
            };
        }
        stats.stockStats[stockCode].fileCount++;

        // 处理关键词数据
        keywordHeaders.forEach((keyword, index) => {
            const count = parseInt(row.cells[index + 2].textContent.trim()) || 0;
            fileStats.keywords[keyword] = count;

            // 更新关键词统计
            stats.keywordStats[keyword].totalCount += count;
            if (count > 0) {
                stats.keywordStats[keyword].filesWithKeyword++;
            }
            stats.keywordStats[keyword].maxCount = Math.max(stats.keywordStats[keyword].maxCount, count);
            stats.keywordStats[keyword].minCount = Math.min(stats.keywordStats[keyword].minCount, count);

            // 更新股票统计
            if (!stats.stockStats[stockCode].keywords[keyword]) {
                stats.stockStats[stockCode].keywords[keyword] = 0;
            }
            stats.stockStats[stockCode].keywords[keyword] += count;
            stats.stockStats[stockCode].totalKeywords += count;
        });

        stats.fileStats.push(fileStats);
    });

    // 修正最小值（如果没有出现过关键词）
    Object.keys(stats.keywordStats).forEach(keyword => {
        if (stats.keywordStats[keyword].minCount === Infinity) {
            stats.keywordStats[keyword].minCount = 0;
        }
    });

    return buildSummaryHTML(stats, keywordHeaders);
}

function generateSummaryFromFullData() {
    if (!fullResultsData) {
        return '<p class="text-muted">没有数据可统计</p>';
    }

    let stats, keywordHeaders;

    // 根据数据格式处理
    if (Array.isArray(fullResultsData)) {
        // 数组格式（从数据库获取的结果）
        stats = processArrayDataForSummary(fullResultsData);
        keywordHeaders = [...new Set(fullResultsData.map(item => item.keyword))].sort();
    } else {
        // 对象格式（直接分析的结果）
        stats = processObjectDataForSummary(fullResultsData);
        keywordHeaders = new Set();
        Object.values(fullResultsData).forEach(stockData => {
            Object.values(stockData).forEach(fileData => {
                Object.keys(fileData).forEach(keyword => keywordHeaders.add(keyword));
            });
        });
        keywordHeaders = Array.from(keywordHeaders).sort();
    }

    // 保存数据到全局变量供分页使用
    currentSummaryStats = stats;
    currentKeywordHeaders = keywordHeaders;

    return buildSummaryHTML(stats, keywordHeaders);
}

function processArrayDataForSummary(results) {
    const stats = {
        totalFiles: new Set(results.map(r => `${r.stock_code}_${r.file_name}`)).size,
        totalStocks: new Set(results.map(r => r.stock_code)).size,
        keywordStats: {},
        stockStats: {},
        fileStats: []
    };

    // 按关键词分组统计
    const keywordGroups = {};
    results.forEach(result => {
        const keyword = result.keyword;
        if (!keywordGroups[keyword]) {
            keywordGroups[keyword] = [];
        }
        keywordGroups[keyword].push(result);
    });

    // 统计每个关键词
    Object.entries(keywordGroups).forEach(([keyword, items]) => {
        const totalCount = items.reduce((sum, item) => sum + (item.count || 0), 0);
        const filesWithKeyword = items.filter(item => (item.count || 0) > 0).length;
        const counts = items.map(item => item.count || 0);
        const maxCount = Math.max(...counts);

        stats.keywordStats[keyword] = {
            totalCount,
            filesWithKeyword,
            maxCount,
            minCount: Math.min(...counts.filter(c => c > 0)) || 0
        };
    });

    // 按股票分组统计
    const stockGroups = {};
    results.forEach(result => {
        const stockCode = result.stock_code;
        if (!stockGroups[stockCode]) {
            stockGroups[stockCode] = {
                fileCount: new Set(),
                totalKeywords: 0,
                keywords: {}
            };
        }
        stockGroups[stockCode].fileCount.add(result.file_name);
        stockGroups[stockCode].totalKeywords += (result.count || 0);

        if (!stockGroups[stockCode].keywords[result.keyword]) {
            stockGroups[stockCode].keywords[result.keyword] = 0;
        }
        stockGroups[stockCode].keywords[result.keyword] += (result.count || 0);
    });

    // 转换股票统计格式
    Object.entries(stockGroups).forEach(([stockCode, data]) => {
        stats.stockStats[stockCode] = {
            fileCount: data.fileCount.size,
            totalKeywords: data.totalKeywords,
            keywords: data.keywords
        };
    });

    return stats;
}

function processObjectDataForSummary(results) {
    const stats = {
        totalFiles: 0,
        totalStocks: Object.keys(results).length,
        keywordStats: {},
        stockStats: {},
        fileStats: []
    };

    // 获取所有关键词
    const allKeywords = new Set();
    Object.values(results).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.keys(fileData).forEach(keyword => allKeywords.add(keyword));
        });
    });

    // 初始化关键词统计
    allKeywords.forEach(keyword => {
        stats.keywordStats[keyword] = {
            totalCount: 0,
            filesWithKeyword: 0,
            maxCount: 0,
            minCount: Infinity
        };
    });

    // 处理每个股票的数据
    Object.entries(results).forEach(([stockCode, stockData]) => {
        stats.stockStats[stockCode] = {
            fileCount: Object.keys(stockData).length,
            totalKeywords: 0,
            keywords: {}
        };

        // 初始化股票的关键词统计
        allKeywords.forEach(keyword => {
            stats.stockStats[stockCode].keywords[keyword] = 0;
        });

        Object.entries(stockData).forEach(([fileName, fileData]) => {
            stats.totalFiles++;
            const fileStats = { stockCode, fileName, keywords: {} };

            allKeywords.forEach(keyword => {
                const count = fileData[keyword] || 0;
                fileStats.keywords[keyword] = count;

                // 更新关键词统计
                stats.keywordStats[keyword].totalCount += count;
                if (count > 0) {
                    stats.keywordStats[keyword].filesWithKeyword++;
                }
                stats.keywordStats[keyword].maxCount = Math.max(stats.keywordStats[keyword].maxCount, count);
                stats.keywordStats[keyword].minCount = Math.min(stats.keywordStats[keyword].minCount, count);

                // 更新股票统计
                stats.stockStats[stockCode].keywords[keyword] += count;
                stats.stockStats[stockCode].totalKeywords += count;
            });

            stats.fileStats.push(fileStats);
        });
    });

    // 修正最小值
    Object.keys(stats.keywordStats).forEach(keyword => {
        if (stats.keywordStats[keyword].minCount === Infinity) {
            stats.keywordStats[keyword].minCount = 0;
        }
    });

    return stats;
}

function buildSummaryHTML(stats, keywordHeaders) {
    let html = '<div class="summary-content">';

    // 添加数据来源说明
    html += `
        <div class="alert alert-info mb-4">
            <i class="bi bi-info-circle"></i>
            <strong>统计说明：</strong>以下统计基于全部分析结果，不受分页显示影响
        </div>
    `;

    // 总体统计
    html += `
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card bg-primary text-white p-3 rounded text-center">
                    <h3>${stats.totalStocks}</h3>
                    <p class="mb-0">股票数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-success text-white p-3 rounded text-center">
                    <h3>${stats.totalFiles}</h3>
                    <p class="mb-0">文件数量</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card bg-info text-white p-3 rounded text-center">
                    <h3>${keywordHeaders.length}</h3>
                    <p class="mb-0">关键词数量</p>
                </div>
            </div>
        </div>
    `;

    // 关键词统计表（带分页）
    html += '<div class="d-flex justify-content-between align-items-center mb-3">';
    html += '<h5 class="mb-0"><i class="bi bi-tags"></i> 关键词统计</h5>';
    html += '<div class="d-flex align-items-center">';
    html += '<label for="keywordPageSize" class="form-label me-2 mb-0">每页显示:</label>';
    html += '<select class="form-select form-select-sm" id="keywordPageSize" style="width: auto;" onchange="updateKeywordPageSize()">';
    html += '<option value="10" selected>10条</option>';
    html += '<option value="20">20条</option>';
    html += '<option value="50">50条</option>';
    html += '<option value="100">100条</option>';
    html += '</select>';
    html += '</div></div>';

    html += '<div id="keywordTableContainer">';
    html += generateKeywordTable(keywordHeaders, stats, 1, 10);
    html += '</div>';

    html += '<div id="keywordPagination" class="d-flex justify-content-center mt-3">';
    html += generateKeywordPagination(keywordHeaders.length, 1, 10);
    html += '</div>';

    // 股票统计表（带分页）
    html += '<div class="d-flex justify-content-between align-items-center mb-3">';
    html += '<h5 class="mb-0"><i class="bi bi-building"></i> 股票统计</h5>';
    html += '<div class="d-flex align-items-center">';
    html += '<label for="stockPageSize" class="form-label me-2 mb-0">每页显示:</label>';
    html += '<select class="form-select form-select-sm" id="stockPageSize" style="width: auto;" onchange="updateStockPageSize()">';
    html += '<option value="10" selected>10条</option>';
    html += '<option value="20">20条</option>';
    html += '<option value="50">50条</option>';
    html += '<option value="100">100条</option>';
    html += '</select>';
    html += '</div></div>';

    const stockEntries = Object.entries(stats.stockStats);
    html += '<div id="stockTableContainer">';
    html += generateStockTable(stockEntries, stats, 1, 10);
    html += '</div>';

    html += '<div id="stockPagination" class="d-flex justify-content-center mt-3">';
    html += generateStockPagination(stockEntries.length, 1, 10);
    html += '</div>';

    // 零出现关键词
    const zeroKeywords = keywordHeaders.filter(keyword => stats.keywordStats[keyword].totalCount === 0);
    if (zeroKeywords.length > 0) {
        html += '<h5><i class="bi bi-exclamation-triangle text-warning"></i> 未出现的关键词</h5>';
        html += '<div class="alert alert-warning">';
        html += '<p>以下关键词在所有文件中都没有出现：</p>';
        html += '<div class="d-flex flex-wrap gap-2">';
        zeroKeywords.forEach(keyword => {
            html += `<span class="badge bg-warning text-dark">${keyword}</span>`;
        });
        html += '</div></div>';
    }

    html += '</div>';
    return html;
}

// 分页相关变量
let currentPage = 1;
let itemsPerPage = 10;
let allTableRows = [];

function addPaginationToResults(tableHtml) {
    // 解析表格HTML，提取行数据
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = tableHtml;
    const table = tempDiv.querySelector('table');

    if (!table) return tableHtml;

    const tbody = table.querySelector('tbody');
    if (!tbody) return tableHtml;

    allTableRows = Array.from(tbody.querySelectorAll('tr'));
    const totalItems = allTableRows.length;

    if (totalItems <= itemsPerPage) {
        // 如果数据少于一页，不需要分页
        return tableHtml;
    }

    // 生成分页后的HTML
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    currentPage = Math.min(currentPage, totalPages); // 确保当前页不超过总页数

    // 获取当前页的数据
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 重新构建表格
    const thead = table.querySelector('thead');
    let paginatedHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    paginatedHtml += thead.outerHTML;
    paginatedHtml += '<tbody>';
    currentPageRows.forEach(row => {
        paginatedHtml += row.outerHTML;
    });
    paginatedHtml += '</tbody></table></div>';

    // 添加分页控件
    paginatedHtml += generatePaginationControls(currentPage, totalPages, totalItems);

    return paginatedHtml;
}

function generatePaginationControls(page, totalPages, totalItems) {
    const startItem = (page - 1) * itemsPerPage + 1;
    const endItem = Math.min(page * itemsPerPage, totalItems);

    let html = '<div class="d-flex justify-content-between align-items-center mt-3">';

    // 显示信息
    html += `<div class="text-muted">显示 ${startItem}-${endItem} 条，共 ${totalItems} 条记录</div>`;

    // 分页控件
    html += '<nav><ul class="pagination pagination-sm mb-0">';

    // 上一页
    const prevDisabled = page === 1 ? 'disabled' : '';
    html += `<li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page - 1}); return false;">上一页</a>
             </li>`;

    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);

    if (startPage > 1) {
        html += '<li class="page-item"><a class="page-link" href="#" onclick="changePage(1); return false;">1</a></li>';
        if (startPage > 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === page ? 'active' : '';
        html += `<li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="changePage(${i}); return false;">${i}</a>
                 </li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="changePage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    const nextDisabled = page === totalPages ? 'disabled' : '';
    html += `<li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${page + 1}); return false;">下一页</a>
             </li>`;

    html += '</ul></nav></div>';

    return html;
}

function changePage(page) {
    if (page < 1 || !allTableRows.length) return;

    const totalPages = Math.ceil(allTableRows.length / itemsPerPage);
    if (page > totalPages) return;

    currentPage = page;

    // 重新生成当前页的表格
    const resultsContent = document.getElementById('resultsContent');
    const table = resultsContent.querySelector('table');
    const thead = table.querySelector('thead');

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allTableRows.length);
    const currentPageRows = allTableRows.slice(startIndex, endIndex);

    // 更新表格内容
    let newHtml = '<div class="table-responsive"><table class="table table-striped results-table">';
    newHtml += thead.outerHTML;
    newHtml += '<tbody>';
    currentPageRows.forEach(row => {
        newHtml += row.outerHTML;
    });
    newHtml += '</tbody></table></div>';

    // 更新分页控件
    newHtml += generatePaginationControls(currentPage, totalPages, allTableRows.length);

    resultsContent.innerHTML = newHtml;

    // 滚动到表格顶部
    document.getElementById('resultsCard').scrollIntoView({ behavior: 'smooth' });
}

function showKeywordContext(keyword, stockCode, fileName = null, analysisId = null) {
    const useAnalysisId = analysisId || currentAnalysisId || currentTaskId;

    console.log('showKeywordContext 调用参数:', {
        keyword, stockCode, fileName, analysisId,
        currentAnalysisId, currentTaskId, useAnalysisId
    });

    if (!useAnalysisId) {
        alert('没有可用的分析ID');
        return;
    }

    showLoading('正在获取上下文...');
    addLog(`正在获取关键词"${keyword}"在${stockCode}的上下文...`, 'info');

    // 获取用户选择的上下文长度
    const contextLengthElement = document.getElementById('contextLength');
    const contextLength = contextLengthElement ? contextLengthElement.value : '100';
    console.log(`🔧 用户选择的上下文长度: ${contextLength}`);

    // 构建URL，包含股票代码和文件名参数
    let url = `/api/keyword_context/${useAnalysisId}/${encodeURIComponent(keyword)}?context_length=${contextLength}`;
    if (stockCode) {
        url += `&stock_code=${encodeURIComponent(stockCode)}`;
    }
    if (fileName) {
        url += `&file_name=${encodeURIComponent(fileName)}`;
    }

    console.log(`🔧 完整的API URL: ${url}`);

    fetch(url)
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭加载弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        console.log('上下文API响应:', data);
        if (data.success) {
            if (data.contexts && data.contexts.length > 0) {
                displayKeywordContext(data.keyword, data.contexts, stockCode);
                addLog(`成功获取到 ${data.contexts.length} 个上下文片段`, 'success');
            } else {
                addLog(`关键词"${keyword}"没有找到上下文内容`, 'warning');
                alert(`关键词"${keyword}"没有找到上下文内容`);
            }
        } else {
            addLog(`获取上下文失败: ${data.message}`, 'error');
            alert(`获取上下文失败: ${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭加载弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);

        console.error('获取上下文失败:', error);
        addLog(`获取上下文请求失败: ${error.message}`, 'error');
        alert(`获取上下文失败: ${error.message}`);
    });
}

function displayKeywordContext(keyword, contexts, targetStockCode) {
    // 过滤指定股票的上下文
    const filteredContexts = targetStockCode ?
        contexts.filter(ctx => ctx.stock_code === targetStockCode) :
        contexts;

    // 计算总片段数
    const totalSnippets = filteredContexts.reduce((sum, context) => sum + (context.snippets ? context.snippets.length : 0), 0);

    // 获取当前设置的上下文长度
    const contextLength = document.getElementById('contextLength').value;
    const contextLengthText = document.getElementById('contextLength').selectedOptions[0].text;

    let html = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0"><i class="bi bi-search"></i> 关键词"${keyword}"的上下文</h6>
            <div>
                <span class="badge bg-info me-2">${totalSnippets} 个片段</span>
                <span class="badge bg-secondary">${contextLengthText}</span>
            </div>
        </div>
    `;

    if (filteredContexts.length === 0) {
        html += '<p class="text-muted">没有找到相关上下文</p>';
    } else {
        filteredContexts.forEach((context, contextIndex) => {
            const snippetCount = context.snippets ? context.snippets.length : 0;
            const keywordCount = context.keyword_count || context.snippets?.length || 0;

            html += `
                <div class="card mb-3" id="context-card-${contextIndex}">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="bi bi-building"></i> ${context.stock_code} - ${context.company_name}
                            </h6>
                            <span class="badge bg-secondary">${keywordCount} 次出现</span>
                        </div>
                        <small class="text-muted">${context.file_name}</small>
                    </div>
                    <div class="card-body">
            `;

            if (context.snippets && context.snippets.length > 0) {
                // 如果片段数量超过5个，添加分页控制
                if (snippetCount > 5) {
                    html += `
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">共 ${snippetCount} 个片段</small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="showAllSnippets(${contextIndex})">
                                        <i class="bi bi-list"></i> 显示全部
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary active" onclick="showPaginatedSnippets(${contextIndex})">
                                        <i class="bi bi-collection"></i> 分页显示
                                    </button>
                                </div>
                            </div>
                            <div id="snippets-container-${contextIndex}">
                                <!-- 片段内容将在这里动态加载 -->
                            </div>
                            <div id="pagination-${contextIndex}" class="d-flex justify-content-center mt-3">
                                <!-- 分页控件将在这里动态加载 -->
                            </div>
                        </div>
                    `;

                    // 存储片段数据到全局变量
                    if (!window.contextSnippets) window.contextSnippets = {};
                    window.contextSnippets[contextIndex] = context.snippets;
                } else {
                    // 少于5个片段，直接显示
                    context.snippets.forEach((snippet, index) => {
                        html += `
                            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                                <small class="text-muted">片段 ${index + 1}:</small>
                                <div class="mt-1">${snippet}</div>
                            </div>
                        `;
                    });
                }
            } else {
                html += '<p class="text-muted">没有找到相关上下文</p>';
            }

            html += '</div></div>';
        });
    }

    // 显示在模态框中
    document.getElementById('summaryContent').innerHTML = html;
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
    document.querySelector('#summaryModal .modal-title').innerHTML = `<i class="bi bi-text-paragraph"></i> 上下文查看`;
    modal.show();

    // 初始化分页显示
    setTimeout(() => {
        filteredContexts.forEach((context, contextIndex) => {
            if (context.snippets && context.snippets.length > 5) {
                showSnippetPage(contextIndex, 1);
            }
        });
    }, 100);
}

function cleanDuplicates() {
    if (!confirm('确定要清理重复数据吗？此操作不可撤销。')) {
        return;
    }

    showLoading('正在清理重复数据...');
    addLog('开始清理重复数据...', 'info');

    fetch('/api/clean_duplicates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭加载弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            addLog(`清理完成: ${data.message}`, 'success');

            // 如果清理了数据，建议刷新页面
            if (data.reports_cleaned > 0 || data.analysis_cleaned > 0) {
                if (confirm('数据已清理完成，是否刷新页面以查看最新结果？')) {
                    location.reload();
                }
            }
        } else {
            addLog(`清理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭加载弹窗（多次尝试）
        setTimeout(() => {
            hideLoading();
        }, 100);
        setTimeout(() => {
            hideLoading();
        }, 500);

        console.error('清理重复数据失败:', error);
        addLog(`清理失败: ${error.message}`, 'error');
    });
}

// 分页显示片段的函数
function showSnippetPage(contextIndex, page) {
    const snippets = window.contextSnippets[contextIndex];
    if (!snippets) return;

    const pageSize = 5;
    const totalPages = Math.ceil(snippets.length / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, snippets.length);

    const containerId = `snippets-container-${contextIndex}`;
    const paginationId = `pagination-${contextIndex}`;

    // 显示当前页的片段
    let snippetsHtml = '';
    for (let i = startIndex; i < endIndex; i++) {
        snippetsHtml += `
            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                <small class="text-muted">片段 ${i + 1}:</small>
                <div class="mt-1">${snippets[i]}</div>
            </div>
        `;
    }

    document.getElementById(containerId).innerHTML = snippetsHtml;

    // 生成分页控件
    let paginationHtml = '';
    if (totalPages > 1) {
        paginationHtml = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

        // 上一页
        if (page > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${page - 1}); return false;">上一页</a></li>`;
        }

        // 页码（显示当前页前后2页）
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, 1); return false;">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${i}); return false;">${i}</a></li>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${totalPages}); return false;">${totalPages}</a></li>`;
        }

        // 下一页
        if (page < totalPages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showSnippetPage(${contextIndex}, ${page + 1}); return false;">下一页</a></li>`;
        }

        paginationHtml += '</ul></nav>';

        // 添加页面信息
        paginationHtml += `<small class="text-muted text-center d-block mt-2">第 ${page} 页，共 ${totalPages} 页</small>`;
    }

    document.getElementById(paginationId).innerHTML = paginationHtml;
}

function showAllSnippets(contextIndex) {
    const snippets = window.contextSnippets[contextIndex];
    if (!snippets) return;

    const containerId = `snippets-container-${contextIndex}`;
    const paginationId = `pagination-${contextIndex}`;

    // 显示所有片段
    let snippetsHtml = '';
    snippets.forEach((snippet, index) => {
        snippetsHtml += `
            <div class="mb-2 p-2 bg-light rounded border-start border-warning border-3">
                <small class="text-muted">片段 ${index + 1}:</small>
                <div class="mt-1">${snippet}</div>
            </div>
        `;
    });

    document.getElementById(containerId).innerHTML = snippetsHtml;
    document.getElementById(paginationId).innerHTML = `<small class="text-muted text-center d-block">显示全部 ${snippets.length} 个片段</small>`;

    // 更新按钮状态
    const cardElement = document.querySelector(`#context-card-${contextIndex}`);
    if (cardElement) {
        const buttons = cardElement.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[0].classList.add('active'); // "显示全部"按钮
    }
}

function showPaginatedSnippets(contextIndex) {
    showSnippetPage(contextIndex, 1);

    // 更新按钮状态
    const cardElement = document.querySelector(`#context-card-${contextIndex}`);
    if (cardElement) {
        const buttons = cardElement.querySelectorAll('.btn-group .btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        buttons[1].classList.add('active'); // "分页显示"按钮
    }
}

// 全局变量存储关联方数据
let currentRelatedPartyData = null;
let currentRelatedPartyPage = 1;
let relatedPartyPageSize = 10;

function displayRelatedPartyAnalysis(relatedPartyData) {
    console.log('🔧 displayRelatedPartyAnalysis 被调用', relatedPartyData);

    // 存储数据到全局变量
    currentRelatedPartyData = relatedPartyData;
    currentRelatedPartyPage = 1;

    console.log('🔧 存储到全局变量完成，数据键数量:', Object.keys(relatedPartyData).length);

    // 创建关联方分析结果卡片（简化版）
    const resultsCard = document.getElementById('resultsCard');

    let relatedPartyCard = document.getElementById('relatedPartyCard');
    if (!relatedPartyCard) {
        relatedPartyCard = document.createElement('div');
        relatedPartyCard.id = 'relatedPartyCard';
        relatedPartyCard.className = 'card mt-3';
        relatedPartyCard.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people-fill"></i> 关联方协同创新分析
                </h5>
                <button type="button" class="btn btn-primary btn-sm" onclick="showRelatedPartyModal()">
                    <i class="bi bi-eye"></i> 查看详细结果
                </button>
            </div>
            <div class="card-body">
                <div id="relatedPartyPreview">
                    <!-- 预览内容 -->
                </div>
            </div>
        `;
        resultsCard.parentNode.insertBefore(relatedPartyCard, resultsCard.nextSibling);
    }

    // 生成预览内容
    const previewContent = generateRelatedPartyPreview(relatedPartyData);
    document.getElementById('relatedPartyPreview').innerHTML = previewContent;
    relatedPartyCard.style.display = 'block';

    // 初始化分页数据
    renderRelatedPartyPage();
}

function generateRelatedPartyHTML(relatedPartyData) {
    let html = '';

    // 统计总体信息
    let totalParties = 0;
    let partiesWithInnovation = 0;
    let partiesNotFound = 0;
    let totalInnovationRelations = 0;

    Object.values(relatedPartyData).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.values(fileData).forEach(partyData => {
                totalParties++;
                if (partyData.not_found) {
                    partiesNotFound++;
                } else if (partyData.has_innovation) {
                    partiesWithInnovation++;
                    totalInnovationRelations += partyData.innovation_contexts.length;
                }
            });
        });
    });

    // 总体统计
    html += `
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-info text-white p-3 rounded text-center">
                    <h4>${totalParties}</h4>
                    <p class="mb-0">关联方总数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success text-white p-3 rounded text-center">
                    <h4>${partiesWithInnovation}</h4>
                    <p class="mb-0">有协同创新</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning text-dark p-3 rounded text-center">
                    <h4>${partiesNotFound}</h4>
                    <p class="mb-0">未找到关联方</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-secondary text-white p-3 rounded text-center">
                    <h4>${totalParties - partiesWithInnovation - partiesNotFound}</h4>
                    <p class="mb-0">无协同创新</p>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="stat-card bg-primary text-white p-3 rounded text-center">
                    <h4>${totalInnovationRelations}</h4>
                    <p class="mb-0">协同创新关系总数</p>
                </div>
            </div>
        </div>
    `;

    // 详细分析结果
    html += '<h6><i class="bi bi-list-ul"></i> 详细分析结果</h6>';

    Object.entries(relatedPartyData).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-building"></i> ${stockCode}
                            <small class="text-muted ms-2">${fileName}</small>
                        </h6>
                    </div>
                    <div class="card-body">
            `;

            Object.entries(fileData).forEach(([partyName, partyData]) => {
                let statusBadge, statusClass, statusIcon;

                if (partyData.not_found) {
                    // 未找到关联方
                    statusBadge = '<span class="badge bg-warning text-dark">未找到关联方</span>';
                    statusClass = 'border-warning';
                    statusIcon = 'bi-exclamation-triangle';
                } else if (partyData.has_innovation) {
                    // 有协同创新 - 添加可点击按钮
                    statusBadge = `<button class="btn btn-success btn-sm" onclick="showInnovationDetails('${stockCode}', '${fileName}', '${partyData.party_name}')" title="查看协同创新详情">
                        <i class="bi bi-check-circle"></i> 有协同创新 (${partyData.innovation_contexts.length}处)
                    </button>`;
                    statusClass = 'border-success';
                    statusIcon = 'bi-check-circle';
                } else {
                    // 找到但无协同创新
                    statusBadge = '<span class="badge bg-secondary">无协同创新</span>';
                    statusClass = 'border-secondary';
                    statusIcon = 'bi-dash-circle';
                }

                html += `
                    <div class="border rounded p-3 mb-3 ${statusClass}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi ${statusIcon}"></i> ${partyData.party_name}
                            </h6>
                            ${statusBadge}
                        </div>
                `;

                if (partyData.not_found) {
                    // 未找到关联方的情况
                    html += `
                        <div class="alert alert-warning border-0 mb-0">
                            <i class="bi bi-info-circle"></i>
                            <strong>未找到该关联方</strong><br>
                            <small class="text-muted">在当前年报中未发现"${partyData.party_name}"的相关信息</small>
                        </div>
                    `;
                } else if (partyData.has_innovation) {
                    // 有协同创新的情况
                    html += `
                        <div class="mb-2">
                            <small class="text-muted">涉及创新关键词：</small>
                            <div class="mt-1">
                    `;
                    partyData.innovation_keywords_found.forEach(keyword => {
                        html += `<span class="badge bg-primary me-1">${keyword}</span>`;
                    });
                    html += '</div></div>';

                    // 显示创新上下文
                    html += '<div class="mt-2">';
                    html += `<small class="text-muted">协同创新上下文 (${partyData.innovation_contexts.length} 处)：</small>`;

                    partyData.innovation_contexts.forEach((context, index) => {
                        html += `
                            <div class="alert alert-light border-start border-success border-3 mt-2">
                                <small class="text-muted">上下文 ${index + 1}:</small>
                                <div class="mt-1">${highlightInnovationKeywords(context.context, context.innovation_keywords)}</div>
                                <small class="text-muted">涉及关键词: ${context.innovation_keywords.join(', ')}</small>
                            </div>
                        `;
                    });
                    html += '</div>';
                } else {
                    // 找到但无协同创新的情况
                    html += `
                        <div class="alert alert-light border-0 mb-0">
                            <i class="bi bi-info-circle"></i>
                            <strong>已找到关联方</strong><br>
                            <small class="text-muted">在年报中找到"${partyData.party_name}"，但未发现协同创新关系</small>
                        </div>
                    `;
                }

                html += '</div>';
            });

            html += '</div></div>';
        });
    });

    return html;
}

function highlightInnovationKeywords(text, keywords) {
    let highlightedText = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(keyword, 'g');
        highlightedText = highlightedText.replace(regex, `<mark class="bg-success text-white">${keyword}</mark>`);
    });
    return highlightedText;
}

function generateRelatedPartyPreview(relatedPartyData) {
    // 生成简化的预览内容
    let totalParties = 0;
    let partiesWithInnovation = 0;
    let partiesNotFound = 0;

    Object.values(relatedPartyData).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.values(fileData).forEach(partyData => {
                totalParties++;
                if (partyData.not_found) {
                    partiesNotFound++;
                } else if (partyData.has_innovation) {
                    partiesWithInnovation++;
                }
            });
        });
    });

    return `
        <div class="row">
            <div class="col-md-3">
                <div class="text-center p-2 bg-info text-white rounded">
                    <h5>${totalParties}</h5>
                    <small>关联方总数</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-2 bg-success text-white rounded">
                    <h5>${partiesWithInnovation}</h5>
                    <small>有协同创新</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-2 bg-warning text-dark rounded">
                    <h5>${partiesNotFound}</h5>
                    <small>未找到</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center p-2 bg-secondary text-white rounded">
                    <h5>${totalParties - partiesWithInnovation - partiesNotFound}</h5>
                    <small>无协同创新</small>
                </div>
            </div>
        </div>
        <div class="text-center mt-3">
            <small class="text-muted">点击"查看详细结果"按钮查看完整分析报告</small>
        </div>
    `;
}

function showRelatedPartyModal() {
    console.log('🔧 showRelatedPartyModal 被调用');
    console.log('🔧 currentRelatedPartyData:', currentRelatedPartyData);

    if (!currentRelatedPartyData) {
        console.error('❌ currentRelatedPartyData 为空');
        return;
    }

    // 显示统计信息
    const statsContent = generateRelatedPartyStats(currentRelatedPartyData);
    document.getElementById('relatedPartyStats').innerHTML = statsContent;
    console.log('🔧 统计信息已设置');

    // 渲染当前页
    renderRelatedPartyPage();
    console.log('🔧 页面渲染完成');

    // 显示模态框
    const modalElement = document.getElementById('relatedPartyModal');
    console.log('🔧 模态框元素:', modalElement);

    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('🔧 模态框已显示');
    } else {
        console.error('❌ 找不到模态框元素 #relatedPartyModal');
    }
}

function generateRelatedPartyStats(relatedPartyData) {
    // 统计总体信息
    let totalParties = 0;
    let partiesWithInnovation = 0;
    let partiesNotFound = 0;
    let totalInnovationRelations = 0;

    Object.values(relatedPartyData).forEach(stockData => {
        Object.values(stockData).forEach(fileData => {
            Object.values(fileData).forEach(partyData => {
                totalParties++;
                if (partyData.not_found) {
                    partiesNotFound++;
                } else if (partyData.has_innovation) {
                    partiesWithInnovation++;
                    totalInnovationRelations += partyData.innovation_contexts.length;
                }
            });
        });
    });

    return `
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card bg-info text-white p-3 rounded text-center">
                    <h4>${totalParties}</h4>
                    <p class="mb-0">关联方总数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success text-white p-3 rounded text-center">
                    <h4>${partiesWithInnovation}</h4>
                    <p class="mb-0">有协同创新</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning text-dark p-3 rounded text-center">
                    <h4>${partiesNotFound}</h4>
                    <p class="mb-0">未找到关联方</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-secondary text-white p-3 rounded text-center">
                    <h4>${totalParties - partiesWithInnovation - partiesNotFound}</h4>
                    <p class="mb-0">无协同创新</p>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="stat-card bg-primary text-white p-3 rounded text-center">
                    <h4>${totalInnovationRelations}</h4>
                    <p class="mb-0">协同创新关系总数</p>
                </div>
            </div>
        </div>
    `;
}

function renderRelatedPartyPage() {
    console.log('🔧 renderRelatedPartyPage 被调用');

    if (!currentRelatedPartyData) {
        console.error('❌ currentRelatedPartyData 为空');
        return;
    }

    // 将数据转换为数组格式便于分页
    const allParties = [];
    Object.entries(currentRelatedPartyData).forEach(([stockCode, stockData]) => {
        Object.entries(stockData).forEach(([fileName, fileData]) => {
            Object.entries(fileData).forEach(([partyName, partyData]) => {
                allParties.push({
                    stockCode,
                    fileName,
                    partyName,
                    partyData
                });
            });
        });
    });

    console.log('🔧 转换后的数组长度:', allParties.length);
    console.log('🔧 当前页面大小:', relatedPartyPageSize);
    console.log('🔧 当前页码:', currentRelatedPartyPage);

    // 计算分页
    const totalItems = allParties.length;
    const totalPages = Math.ceil(totalItems / relatedPartyPageSize);
    const startIndex = (currentRelatedPartyPage - 1) * relatedPartyPageSize;
    const endIndex = Math.min(startIndex + relatedPartyPageSize, totalItems);
    const currentPageItems = allParties.slice(startIndex, endIndex);

    console.log('🔧 分页计算结果:', {
        totalItems,
        totalPages,
        startIndex,
        endIndex,
        currentPageItemsLength: currentPageItems.length
    });

    // 渲染当前页内容
    let html = '';
    currentPageItems.forEach(item => {
        html += generateRelatedPartyItem(item.stockCode, item.fileName, item.partyData);
    });

    const contentElement = document.getElementById('relatedPartyContent');
    if (contentElement) {
        contentElement.innerHTML = html;
        console.log('🔧 内容已设置到 relatedPartyContent');
    } else {
        console.error('❌ 找不到元素 #relatedPartyContent');
    }

    // 更新页面信息
    const pageInfoElement = document.getElementById('relatedPartyPageInfo');
    if (pageInfoElement) {
        pageInfoElement.textContent = `第 ${currentRelatedPartyPage} 页，共 ${totalPages} 页 (${totalItems} 条记录)`;
        console.log('🔧 页面信息已更新');
    } else {
        console.error('❌ 找不到元素 #relatedPartyPageInfo');
    }

    // 渲染分页控件
    renderRelatedPartyPagination(totalPages);
}

function generateRelatedPartyItem(stockCode, fileName, partyData) {
    let statusBadge, statusClass, statusIcon;

    if (partyData.not_found) {
        statusBadge = '<span class="badge bg-warning text-dark">未找到关联方</span>';
        statusClass = 'border-warning';
        statusIcon = 'bi-exclamation-triangle';
    } else if (partyData.has_innovation) {
        statusBadge = '<span class="badge bg-success">有协同创新</span>';
        statusClass = 'border-success';
        statusIcon = 'bi-check-circle';
    } else {
        statusBadge = '<span class="badge bg-secondary">无协同创新</span>';
        statusClass = 'border-secondary';
        statusIcon = 'bi-dash-circle';
    }

    let html = `
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-building"></i> ${stockCode}
                    <small class="text-muted ms-2">${fileName}</small>
                </h6>
            </div>
            <div class="card-body">
                <div class="border rounded p-3 ${statusClass}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">
                            <i class="bi ${statusIcon}"></i> ${partyData.party_name}
                        </h6>
                        ${statusBadge}
                    </div>
    `;

    if (partyData.not_found) {
        html += `
            <div class="alert alert-warning border-0 mb-0">
                <i class="bi bi-info-circle"></i>
                <strong>未找到该关联方</strong><br>
                <small class="text-muted">在当前年报中未发现"${partyData.party_name}"的相关信息</small>
            </div>
        `;
    } else if (partyData.has_innovation) {
        html += `
            <div class="mb-2">
                <small class="text-muted">涉及创新关键词：</small>
                <div class="mt-1">
        `;
        partyData.innovation_keywords_found.forEach(keyword => {
            html += `<span class="badge bg-primary me-1">${keyword}</span>`;
        });
        html += '</div></div>';

        html += '<div class="mt-2">';
        html += `<small class="text-muted">协同创新上下文 (${partyData.innovation_contexts.length} 处)：</small>`;

        partyData.innovation_contexts.forEach((context, index) => {
            html += `
                <div class="alert alert-light border-start border-success border-3 mt-2">
                    <small class="text-muted">上下文 ${index + 1}:</small>
                    <div class="mt-1">${highlightInnovationKeywords(context.context, context.innovation_keywords)}</div>
                    <small class="text-muted">涉及关键词: ${context.innovation_keywords.join(', ')}</small>
                </div>
            `;
        });
        html += '</div>';
    } else {
        html += `
            <div class="alert alert-light border-0 mb-0">
                <i class="bi bi-info-circle"></i>
                <strong>已找到关联方</strong><br>
                <small class="text-muted">在年报中找到"${partyData.party_name}"，但未发现协同创新关系</small>
            </div>
        `;
    }

    html += '</div></div></div>';
    return html;
}

function renderRelatedPartyPagination(totalPages) {
    if (totalPages <= 1) {
        document.getElementById('relatedPartyPagination').innerHTML = '';
        return;
    }

    let html = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

    // 上一页
    if (currentRelatedPartyPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToRelatedPartyPage(${currentRelatedPartyPage - 1}); return false;">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentRelatedPartyPage - 2);
    const endPage = Math.min(totalPages, currentRelatedPartyPage + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToRelatedPartyPage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentRelatedPartyPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="goToRelatedPartyPage(${i}); return false;">${i}</a></li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToRelatedPartyPage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    if (currentRelatedPartyPage < totalPages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToRelatedPartyPage(${currentRelatedPartyPage + 1}); return false;">下一页</a></li>`;
    }

    html += '</ul></nav>';
    document.getElementById('relatedPartyPagination').innerHTML = html;
}

function goToRelatedPartyPage(page) {
    currentRelatedPartyPage = page;
    renderRelatedPartyPage();
}

function updateRelatedPartyPageSize() {
    relatedPartyPageSize = parseInt(document.getElementById('relatedPartyPageSize').value);
    currentRelatedPartyPage = 1; // 重置到第一页
    renderRelatedPartyPage();
}

// 关键词表格生成函数
function generateKeywordTable(keywordHeaders, stats, page, pageSize) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, keywordHeaders.length);
    const currentPageKeywords = keywordHeaders.slice(startIndex, endIndex);

    let html = '<div class="table-responsive">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>关键词</th><th>总出现次数</th><th>出现文件数</th><th>最大次数</th><th>平均次数</th><th>覆盖率</th></tr></thead><tbody>';

    currentPageKeywords.forEach(keyword => {
        const stat = stats.keywordStats[keyword];
        const avgCount = stat.filesWithKeyword > 0 ? (stat.totalCount / stat.filesWithKeyword).toFixed(1) : 0;
        const coverage = ((stat.filesWithKeyword / stats.totalFiles) * 100).toFixed(1);
        const rowClass = stat.totalCount === 0 ? 'table-secondary' : '';

        html += `
            <tr class="${rowClass}">
                <td><strong>${keyword}</strong></td>
                <td class="text-center">${stat.totalCount}</td>
                <td class="text-center">${stat.filesWithKeyword}</td>
                <td class="text-center">${stat.maxCount}</td>
                <td class="text-center">${avgCount}</td>
                <td class="text-center">${coverage}%</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 添加页面信息
    const totalPages = Math.ceil(keywordHeaders.length / pageSize);
    html += `<div class="text-center text-muted small mt-2">第 ${page} 页，共 ${totalPages} 页 (${keywordHeaders.length} 个关键词)</div>`;

    return html;
}

// 股票表格生成函数
function generateStockTable(stockEntries, stats, page, pageSize) {
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, stockEntries.length);
    const currentPageStocks = stockEntries.slice(startIndex, endIndex);

    let html = '<div class="table-responsive">';
    html += '<table class="table table-sm table-striped">';
    html += '<thead><tr><th>股票代码</th><th>文件数</th><th>关键词总数</th><th>平均每文件</th></tr></thead><tbody>';

    currentPageStocks.forEach(([stockCode, stockStat]) => {
        const avgPerFile = stockStat.fileCount > 0 ? (stockStat.totalKeywords / stockStat.fileCount).toFixed(1) : 0;
        html += `
            <tr>
                <td><strong>${stockCode}</strong></td>
                <td class="text-center">${stockStat.fileCount}</td>
                <td class="text-center">${stockStat.totalKeywords}</td>
                <td class="text-center">${avgPerFile}</td>
            </tr>
        `;
    });
    html += '</tbody></table></div>';

    // 添加页面信息
    const totalPages = Math.ceil(stockEntries.length / pageSize);
    html += `<div class="text-center text-muted small mt-2">第 ${page} 页，共 ${totalPages} 页 (${stockEntries.length} 个股票)</div>`;

    return html;
}

// 关键词分页控件生成函数
function generateKeywordPagination(totalItems, currentPage, pageSize) {
    const totalPages = Math.ceil(totalItems / pageSize);
    if (totalPages <= 1) return '';

    let html = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

    // 上一页
    if (currentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToKeywordPage(${currentPage - 1}); return false;">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToKeywordPage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="goToKeywordPage(${i}); return false;">${i}</a></li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToKeywordPage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToKeywordPage(${currentPage + 1}); return false;">下一页</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

// 股票分页控件生成函数
function generateStockPagination(totalItems, currentPage, pageSize) {
    const totalPages = Math.ceil(totalItems / pageSize);
    if (totalPages <= 1) return '';

    let html = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

    // 上一页
    if (currentPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToStockPage(${currentPage - 1}); return false;">上一页</a></li>`;
    }

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToStockPage(1); return false;">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="goToStockPage(${i}); return false;">${i}</a></li>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToStockPage(${totalPages}); return false;">${totalPages}</a></li>`;
    }

    // 下一页
    if (currentPage < totalPages) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="goToStockPage(${currentPage + 1}); return false;">下一页</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

// 分页控制函数
function goToKeywordPage(page) {
    currentKeywordPage = page;
    updateKeywordTable();
}

function goToStockPage(page) {
    currentStockPage = page;
    updateStockTable();
}

function updateKeywordPageSize() {
    keywordPageSize = parseInt(document.getElementById('keywordPageSize').value);
    currentKeywordPage = 1;
    updateKeywordTable();
}

function updateStockPageSize() {
    stockPageSize = parseInt(document.getElementById('stockPageSize').value);
    currentStockPage = 1;
    updateStockTable();
}

function updateKeywordTable() {
    if (!currentSummaryStats || !currentKeywordHeaders) return;

    const tableHtml = generateKeywordTable(currentKeywordHeaders, currentSummaryStats, currentKeywordPage, keywordPageSize);
    const paginationHtml = generateKeywordPagination(currentKeywordHeaders.length, currentKeywordPage, keywordPageSize);

    document.getElementById('keywordTableContainer').innerHTML = tableHtml;
    document.getElementById('keywordPagination').innerHTML = paginationHtml;
}

function updateStockTable() {
    if (!currentSummaryStats) return;

    const stockEntries = Object.entries(currentSummaryStats.stockStats);
    const tableHtml = generateStockTable(stockEntries, currentSummaryStats, currentStockPage, stockPageSize);
    const paginationHtml = generateStockPagination(stockEntries.length, currentStockPage, stockPageSize);

    document.getElementById('stockTableContainer').innerHTML = tableHtml;
    document.getElementById('stockPagination').innerHTML = paginationHtml;
}

function showInnovationDetails(stockCode, fileName, partyName) {
    console.log('🔍 查看协同创新详情:', { stockCode, fileName, partyName });

    if (!currentRelatedPartyData) {
        alert('没有关联方数据');
        return;
    }

    // 查找对应的关联方数据
    let partyData = null;
    if (currentRelatedPartyData[stockCode] &&
        currentRelatedPartyData[stockCode][fileName] &&
        currentRelatedPartyData[stockCode][fileName][partyName]) {
        partyData = currentRelatedPartyData[stockCode][fileName][partyName];
    }

    if (!partyData || !partyData.has_innovation) {
        alert('未找到协同创新数据');
        return;
    }

    // 生成详情HTML
    const detailsHtml = generateInnovationDetailsHTML(stockCode, fileName, partyData);

    // 显示在模态框中
    document.getElementById('summaryContent').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('summaryModal'));
    document.querySelector('#summaryModal .modal-title').innerHTML = `<i class="bi bi-lightbulb"></i> 协同创新详情`;
    modal.show();
}

function generateInnovationDetailsHTML(stockCode, fileName, partyData) {
    let html = `
        <div class="innovation-details">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>股票代码:</strong><br>
                            <span class="badge bg-info fs-6">${stockCode}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>关联方:</strong><br>
                            <span class="badge bg-success fs-6">${partyData.party_name}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>协同创新关系:</strong><br>
                            <span class="badge bg-warning text-dark fs-6">${partyData.innovation_contexts.length} 处</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <strong>年报文件:</strong><br>
                            <small class="text-muted">${fileName}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 涉及的创新关键词 -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-tags"></i> 涉及的创新关键词
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
    `;

    partyData.innovation_keywords_found.forEach(keyword => {
        html += `<span class="badge bg-primary fs-6">${keyword}</span>`;
    });

    html += `
                    </div>
                </div>
            </div>

            <!-- 协同创新上下文详情 -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="bi bi-text-paragraph"></i> 协同创新上下文详情
                    </h5>
                </div>
                <div class="card-body">
    `;

    partyData.innovation_contexts.forEach((context, index) => {
        html += `
            <div class="context-item mb-4 p-3 border rounded ${index % 2 === 0 ? 'bg-light' : 'bg-white'}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">
                        <i class="bi bi-chat-quote"></i> 上下文片段 ${index + 1}
                    </h6>
                    <div class="d-flex gap-1">
        `;

        context.innovation_keywords.forEach(keyword => {
            html += `<span class="badge bg-success">${keyword}</span>`;
        });

        html += `
                    </div>
                </div>
                <div class="context-content p-3 bg-white border rounded">
                    <div class="text-content">
                        ${highlightInnovationKeywords(context.context, context.innovation_keywords)}
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        涉及关键词: ${context.innovation_keywords.join(', ')}
                    </small>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="text-center">
                <button type="button" class="btn btn-secondary" onclick="goBackToRelatedPartyModal()">
                    <i class="bi bi-arrow-left"></i> 返回关联方分析
                </button>
            </div>
        </div>
    `;

    return html;
}

function goBackToRelatedPartyModal() {
    // 关闭当前模态框
    const currentModal = bootstrap.Modal.getInstance(document.getElementById('summaryModal'));
    if (currentModal) {
        currentModal.hide();
    }

    // 重新显示关联方分析模态框
    setTimeout(() => {
        showRelatedPartyModal();
    }, 300);
}

// AI分析相关函数
function openAiAnalysisModal() {
    const modal = new bootstrap.Modal(document.getElementById('aiAnalysisModal'));
    modal.show();

    // 首次进入时自动获取模型列表
    setTimeout(() => {
        autoGetModelsOnFirstOpen();
    }, 500); // 延迟500ms确保模态框完全显示
}

function clearAiForm() {
    document.getElementById('aiStockCodes').value = '';
    document.getElementById('aiKeywords').value = '';
    document.getElementById('aiRelatedParties').value = '';
    document.getElementById('aiPrompt').value = '根据搜索到的全部关联方公司的上下文内容，判断股票代码对应的公司和哪些关联方公司存在协同创新。';

    // 清空AI分析结果
    const resultsDiv = document.getElementById('aiAnalysisResults');
    const placeholderDiv = document.getElementById('aiAnalysisPlaceholder');
    const contextSection = document.getElementById('contextSection');

    resultsDiv.style.display = 'none';
    if (placeholderDiv) placeholderDiv.style.display = 'flex';
    if (contextSection) contextSection.style.display = 'none';

    document.getElementById('aiStreamingContent').innerHTML = '';
    document.getElementById('contextContent').innerHTML = '';
    document.getElementById('contextCount').textContent = '0';
    document.getElementById('aiStreamingIndicator').style.display = 'none';

    // 重置AI分析状态
    resetAiAnalysisState();

    // 折叠上下文引用
    const contextReferences = document.getElementById('contextReferences');
    const contextToggleIcon = document.getElementById('contextToggleIcon');
    if (contextReferences && contextReferences.classList.contains('show')) {
        const bsCollapse = new bootstrap.Collapse(contextReferences, {
            toggle: false
        });
        bsCollapse.hide();
    }
    if (contextToggleIcon) {
        contextToggleIcon.style.transform = 'rotate(0deg)';
    }

    // 重置AI配置
    const modelSelect = document.getElementById('openaiModel');
    if (modelSelect.options.length > 0) {
        // 尝试选择claude-sonnet-4，如果不存在则选择第一个选项
        const claudeOption = Array.from(modelSelect.options).find(opt => opt.value === 'claude-sonnet-4');
        if (claudeOption) {
            modelSelect.value = 'claude-sonnet-4';
        } else {
            modelSelect.selectedIndex = 0;
        }
    }
}



function testOpenAIConnection() {
    const model = document.getElementById('openaiModel').value;

    showLoading('正在测试OpenAI连接（使用服务器配置）...');
    addLog('开始测试OpenAI连接（使用服务器配置）...', 'info');

    const requestData = {
        use_default: true,
        model: model
    };

    fetch('/api/test_openai', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            addLog(`OpenAI连接测试成功: ${data.message}`, 'success');
            alert(`连接测试成功！\n${data.message}`);
        } else {
            addLog(`OpenAI连接测试失败: ${data.message}`, 'error');
            alert(`连接测试失败：${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        addLog(`OpenAI连接测试请求失败: ${error.message}`, 'error');
        alert(`连接测试请求失败：${error.message}`);
    });
}

function testDefaultOpenAIConnection() {
    showLoading('正在测试服务器默认OpenAI配置...');
    addLog('开始测试服务器默认OpenAI配置...', 'info');

    // 发送空的配置，让服务器使用默认配置
    const requestData = {
        use_default: true
    };

    fetch('/api/test_openai', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            addLog(`服务器默认OpenAI配置测试成功: ${data.message}`, 'success');
            alert(`默认配置测试成功！\n${data.message}`);
        } else {
            addLog(`服务器默认OpenAI配置测试失败: ${data.message}`, 'error');
            alert(`默认配置测试失败：${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        addLog(`服务器默认OpenAI配置测试请求失败: ${error.message}`, 'error');
        alert(`默认配置测试请求失败：${error.message}`);
    });
}

function testOpenAISimple() {
    const model = document.getElementById('openaiModel').value;

    showLoading('正在调试OpenAI API（使用服务器配置）...');
    addLog('开始OpenAI API调试测试（使用服务器配置）...', 'info');

    const requestData = {
        model: model
    };

    fetch('/api/test_openai_simple', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            addLog(`OpenAI API调试成功: ${data.message}`, 'success');

            let debugMessage = `API调试成功！\n\n`;
            debugMessage += `响应内容: ${data.response}\n`;
            debugMessage += `内容长度: ${data.content_length} 字符\n`;
            if (data.chunks_received !== undefined) {
                debugMessage += `接收块数: ${data.chunks_received}\n`;
            }
            debugMessage += `\n详细信息请查看控制台日志。`;

            alert(debugMessage);
        } else {
            addLog(`OpenAI API调试失败: ${data.message}`, 'error');
            alert(`API调试失败：\n${data.message}\n\n请检查服务器配置或查看控制台日志。`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        addLog(`OpenAI API调试请求失败: ${error.message}`, 'error');
        alert(`API调试请求失败：${error.message}`);
    });
}

function startAiAnalysis() {
    const stockCodes = document.getElementById('aiStockCodes').value.trim();
    const keywords = document.getElementById('aiKeywords').value.trim();
    const relatedParties = document.getElementById('aiRelatedParties').value.trim();
    const prompt = document.getElementById('aiPrompt').value.trim();

    if (!stockCodes) {
        alert('请输入股票代码');
        return;
    }

    if (!keywords) {
        alert('请输入统计关键词');
        return;
    }

    if (!prompt) {
        alert('请输入AI分析要求');
        return;
    }

    // 不显示弹窗加载，直接开始流式分析
    addLog('开始AI分析...', 'info');

    // 获取AI配置
    const model = document.getElementById('openaiModel').value;

    // 使用服务器默认配置，只传递模型，深度思考默认关闭
    const openaiConfig = {
        model: model,
        enable_thinking: false
    };

    console.log('使用服务器默认配置，模型:', model, '深度思考: 关闭');

    const requestData = {
        stock_codes: stockCodes,
        keywords: keywords,
        related_parties: relatedParties,
        prompt: prompt,
        openai_config: openaiConfig
    };

    // 使用真正的流式响应
    startRealStreamingAnalysis(requestData);
}

function displayAiAnalysisResults(analysisResult) {
    const resultsDiv = document.getElementById('aiAnalysisResults');
    const streamingContentDiv = document.getElementById('aiStreamingContent');
    const contextContentDiv = document.getElementById('contextContent');
    const contextCountSpan = document.getElementById('contextCount');

    // 显示结果区域
    resultsDiv.style.display = 'block';

    // 显示上下文引用
    if (analysisResult.contexts && analysisResult.contexts.length > 0) {
        displayContextReferences(analysisResult.contexts);
        contextCountSpan.textContent = analysisResult.contexts.length;
    } else {
        contextContentDiv.innerHTML = '<p class="text-muted">未找到相关上下文</p>';
        contextCountSpan.textContent = '0';
    }

    // 开始流式显示AI分析结果
    if (analysisResult.ai_response) {
        startStreamingResponse(analysisResult.ai_response);
    } else {
        streamingContentDiv.innerHTML = '<p class="text-muted">AI分析结果为空</p>';
    }
}

// 全局变量存储AI分析上下文数据
let aiContextData = {
    withKeywords: [],
    relatedPartyOnly: [],
    currentWithKeywordsPage: 1,
    currentRelatedPartyPage: 1,
    pageSize: 5
};

// AI分析界面状态
let aiAnalysisState = {
    isMarkdownView: true,  // 默认启用markdown视图以获得更好的显示效果
    rawContent: '',
    markdownContent: '',
    startTime: null,       // 分析开始时间
    endTime: null,         // 分析结束时间
    duration: 0            // 分析耗时（秒）
};

// AI分析历史记录状态
let analysisHistoryState = {
    histories: [],
    selectedHistoryId: null,
    isHistoryMarkdownView: false
};

function displayContextReferences(contexts) {
    console.log('🔧 displayContextReferences 开始处理:', contexts);

    const contextContentDiv = document.getElementById('contextContent');
    const contextSection = document.getElementById('contextSection');
    const contextCountSpan = document.getElementById('contextCount');

    // 验证DOM元素
    if (!contextContentDiv) {
        console.error('❌ contextContent 元素未找到');
        return;
    }
    if (!contextSection) {
        console.error('❌ contextSection 元素未找到');
        return;
    }
    if (!contextCountSpan) {
        console.error('❌ contextCount 元素未找到');
        return;
    }

    // 验证上下文数据
    if (!contexts || !Array.isArray(contexts)) {
        console.error('❌ 上下文数据无效:', contexts);
        contextSection.style.display = 'none';
        return;
    }

    console.log(`📊 处理 ${contexts.length} 个上下文`);

    // 分类上下文并存储到全局变量
    aiContextData.withKeywords = contexts.filter(ctx => ctx.context_type === 'with_keywords');
    aiContextData.relatedPartyOnly = contexts.filter(ctx => ctx.context_type === 'related_party_only');
    aiContextData.currentWithKeywordsPage = 1;
    aiContextData.currentRelatedPartyPage = 1;

    console.log(`📊 上下文分类结果: 包含关键词=${aiContextData.withKeywords.length}, 仅关联方=${aiContextData.relatedPartyOnly.length}`);

    // 更新上下文计数
    contextCountSpan.textContent = contexts.length;

    // 如果有上下文，显示上下文部分
    if (contexts.length > 0) {
        contextSection.style.display = 'block';
        console.log('✅ 上下文部分已设置为可见');
    } else {
        contextSection.style.display = 'none';
        console.log('⚠️ 没有上下文数据，隐藏上下文部分');
        return;
    }

    let html = '';

    // 显示包含关键词的上下文
    if (aiContextData.withKeywords.length > 0) {
        html += `
            <div class="context-section mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="text-success mb-0">
                        <i class="bi bi-check-circle"></i>
                        包含关键词的上下文 (${aiContextData.withKeywords.length})
                    </h6>
                    ${aiContextData.withKeywords.length > aiContextData.pageSize ? `
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-success" onclick="showAllAiContexts('withKeywords')">
                                <i class="bi bi-list"></i> 显示全部
                            </button>
                            <button type="button" class="btn btn-outline-success active" onclick="showPaginatedAiContexts('withKeywords')">
                                <i class="bi bi-collection"></i> 分页显示
                            </button>
                        </div>
                    ` : ''}
                </div>
                <div id="withKeywords-container">
                    <!-- 包含关键词的上下文将在这里显示 -->
                </div>
                <div id="withKeywords-pagination" class="d-flex justify-content-center mt-3">
                    <!-- 分页控件将在这里显示 -->
                </div>
            </div>
        `;
    }

    // 显示仅包含关联方名称的上下文
    if (aiContextData.relatedPartyOnly.length > 0) {
        html += `
            <div class="context-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="text-info mb-1">
                            <i class="bi bi-info-circle"></i>
                            仅关联方名称的上下文 (${aiContextData.relatedPartyOnly.length})
                        </h6>
                        <p class="text-muted small mb-0">以下上下文仅包含关联方名称，可能包含其他形式的合作关系</p>
                    </div>
                    ${aiContextData.relatedPartyOnly.length > aiContextData.pageSize ? `
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-info" onclick="showAllAiContexts('relatedPartyOnly')">
                                <i class="bi bi-list"></i> 显示全部
                            </button>
                            <button type="button" class="btn btn-outline-info active" onclick="showPaginatedAiContexts('relatedPartyOnly')">
                                <i class="bi bi-collection"></i> 分页显示
                            </button>
                        </div>
                    ` : ''}
                </div>
                <div id="relatedPartyOnly-container">
                    <!-- 仅关联方的上下文将在这里显示 -->
                </div>
                <div id="relatedPartyOnly-pagination" class="d-flex justify-content-center mt-3">
                    <!-- 分页控件将在这里显示 -->
                </div>
            </div>
        `;
    }

    if (contexts.length === 0) {
        html = '<p class="text-muted">未找到相关上下文</p>';
    }

    contextContentDiv.innerHTML = html;

    // 初始化分页显示
    setTimeout(() => {
        if (aiContextData.withKeywords.length > 0) {
            showAiContextPage('withKeywords', 1);
        }
        if (aiContextData.relatedPartyOnly.length > 0) {
            showAiContextPage('relatedPartyOnly', 1);
        }
    }, 100);
}

function startStreamingResponse(fullResponse) {
    const streamingContentDiv = document.getElementById('aiStreamingContent');
    const streamingIndicator = document.getElementById('aiStreamingIndicator');

    // 清空内容并显示指示器
    streamingContentDiv.innerHTML = '';
    streamingIndicator.style.display = 'block';

    // 添加光标
    const cursor = document.createElement('span');
    cursor.className = 'streaming-cursor';
    streamingContentDiv.appendChild(cursor);

    // 模拟流式响应
    let currentIndex = 0;
    const streamingSpeed = getRandomStreamingSpeed(); // 随机速度使其更自然

    function typeNextChunk() {
        if (currentIndex < fullResponse.length) {
            // 移除光标
            if (cursor.parentNode) {
                cursor.parentNode.removeChild(cursor);
            }

            // 计算下一个块的大小（随机化以模拟真实流式响应）
            const nextChunkSize = getRandomChunkSize();
            const nextChunk = fullResponse.slice(currentIndex, currentIndex + nextChunkSize);

            // 添加文本
            const textNode = document.createTextNode(nextChunk);
            streamingContentDiv.appendChild(textNode);

            // 重新添加光标
            streamingContentDiv.appendChild(cursor);

            currentIndex += nextChunkSize;

            // 滚动到底部
            streamingContentDiv.scrollTop = streamingContentDiv.scrollHeight;

            // 随机延迟以模拟真实的流式响应
            const delay = getRandomDelay();
            setTimeout(typeNextChunk, delay);
        } else {
            // 流式响应完成，移除光标和指示器
            if (cursor.parentNode) {
                cursor.parentNode.removeChild(cursor);
            }
            streamingIndicator.style.display = 'none';
            addLog('AI分析流式响应完成', 'success');
        }
    }

    // 开始流式响应
    setTimeout(typeNextChunk, 500); // 初始延迟
}

function getRandomStreamingSpeed() {
    // 返回随机的流式速度（字符数）
    return Math.floor(Math.random() * 20) + 10; // 10-30字符
}

function getRandomChunkSize() {
    // 返回随机的块大小
    return Math.floor(Math.random() * 15) + 5; // 5-20字符
}

function getRandomDelay() {
    // 返回随机的延迟时间
    return Math.floor(Math.random() * 100) + 30; // 30-130毫秒
}

function startRealStreamingAnalysis(requestData) {
    const resultsDiv = document.getElementById('aiAnalysisResults');
    const placeholderDiv = document.getElementById('aiAnalysisPlaceholder');
    const streamingContentDiv = document.getElementById('aiStreamingContent');
    const streamingIndicator = document.getElementById('aiStreamingIndicator');
    const contextContentDiv = document.getElementById('contextContent');
    const contextCountSpan = document.getElementById('contextCount');
    const contextSection = document.getElementById('contextSection');

    // 重置AI分析状态
    resetAiAnalysisState();

    // 记录开始时间
    aiAnalysisState.startTime = Date.now();

    // 隐藏占位符，显示结果区域
    if (placeholderDiv) placeholderDiv.style.display = 'none';
    resultsDiv.style.display = 'block';

    // 清空内容并显示流式指示器
    streamingContentDiv.innerHTML = '';
    streamingContentDiv.classList.remove('markdown-content');
    streamingIndicator.style.display = 'block';

    // 隐藏上下文部分，清空之前的上下文
    if (contextSection) contextSection.style.display = 'none';
    contextContentDiv.innerHTML = '';
    contextCountSpan.textContent = '0';

    // 添加光标
    const cursor = document.createElement('span');
    cursor.className = 'streaming-cursor';
    streamingContentDiv.appendChild(cursor);

    let contexts = [];
    let isComplete = false;
    let historySaved = false; // 防止重复保存历史记录

    // 记录请求信息
    console.log('开始AI分析流式请求:', requestData);
    addLog('发送AI分析请求...', 'info');

    // 创建请求
    fetch('/api/ai_analysis_stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        function readStream() {
            return reader.read().then(({ done, value }) => {
                if (done) {
                    // 流结束
                    handleStreamComplete();
                    return;
                }

                // 解析流数据
                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            handleStreamData(data);
                        } catch (e) {
                            console.warn('解析流数据失败:', line, e);
                        }
                    }
                }

                // 继续读取
                return readStream();
            });
        }

        return readStream();
    })
    .catch(error => {
        console.error('流式请求失败:', error);

        // 隐藏流式指示器
        streamingIndicator.style.display = 'none';

        // 移除光标
        if (cursor.parentNode) {
            cursor.parentNode.removeChild(cursor);
        }

        // 重置界面状态：隐藏结果区域，显示占位符
        resultsDiv.style.display = 'none';
        if (placeholderDiv) placeholderDiv.style.display = 'flex';
        if (contextSection) contextSection.style.display = 'none';

        // 清空内容
        streamingContentDiv.innerHTML = '';
        contextContentDiv.innerHTML = '';
        contextCountSpan.textContent = '0';

        // 记录错误日志
        addLog(`AI分析请求失败: ${error.message}`, 'error');

        // 显示用户友好的错误提示
        alert(`AI分析请求失败: ${error.message}\n\n请检查网络连接或稍后重试。`);
    });

    function handleStreamData(data) {
        console.log('收到流式数据:', data);

        switch (data.type) {
            case 'status':
                addLog(data.message, 'info');
                break;

            case 'contexts':
                console.log('🔍 处理上下文数据:', data.data);

                // 验证上下文数据
                if (!data.data || !Array.isArray(data.data)) {
                    console.error('❌ 上下文数据格式错误:', data.data);
                    addLog('上下文数据格式错误', 'error');
                    break;
                }

                contexts = data.data;
                console.log(`📊 上下文数据统计: 总数=${contexts.length}`);

                // 分析上下文类型分布
                const withKeywords = contexts.filter(ctx => ctx.context_type === 'with_keywords');
                const relatedPartyOnly = contexts.filter(ctx => ctx.context_type === 'related_party_only');
                console.log(`📊 上下文类型分布: 包含关键词=${withKeywords.length}, 仅关联方=${relatedPartyOnly.length}`);

                // 显示上下文引用
                try {
                    displayContextReferences(contexts);
                    contextCountSpan.textContent = contexts.length;

                    // 确保上下文部分可见
                    if (contextSection && contexts.length > 0) {
                        contextSection.style.display = 'block';
                        console.log('✅ 上下文部分已显示');
                    }

                    addLog(`找到 ${contexts.length} 个相关上下文`, 'success');
                    console.log('✅ 上下文引用显示完成');
                } catch (error) {
                    console.error('❌ 显示上下文引用失败:', error);
                    addLog('显示上下文引用失败', 'error');
                }
                break;

            case 'ai_chunk':
                // 检查数据是否为空
                if (!data.data || data.data.trim() === '') {
                    console.warn('收到空的AI响应块');
                    break;
                }

                // 累积原始内容
                aiAnalysisState.rawContent += data.data;

                // 移除光标
                if (cursor.parentNode) {
                    cursor.parentNode.removeChild(cursor);
                }

                // 根据当前视图模式显示内容
                if (aiAnalysisState.isMarkdownView) {
                    // Markdown视图：解析并显示
                    try {
                        // 使用安全的markdown解析
                        const markdownHtml = safeMarkdownParse(aiAnalysisState.rawContent);
                        streamingContentDiv.innerHTML = markdownHtml;
                        // 代码高亮
                        streamingContentDiv.querySelectorAll('pre code').forEach((block) => {
                            try {
                                hljs.highlightElement(block);
                            } catch (highlightError) {
                                console.warn('代码高亮失败:', highlightError);
                            }
                        });
                        streamingContentDiv.classList.add('markdown-content');
                    } catch (e) {
                        console.warn('Markdown解析失败:', e);
                        console.warn('原始内容:', aiAnalysisState.rawContent.substring(0, 200) + '...');
                        // 回退到纯文本
                        streamingContentDiv.textContent = aiAnalysisState.rawContent;
                        streamingContentDiv.classList.remove('markdown-content');
                    }
                } else {
                    // 纯文本视图：直接添加文本
                    const textNode = document.createTextNode(data.data);
                    streamingContentDiv.appendChild(textNode);
                    streamingContentDiv.classList.remove('markdown-content');
                }

                // 重新添加光标
                streamingContentDiv.appendChild(cursor);

                // 滚动到底部
                streamingContentDiv.scrollTop = streamingContentDiv.scrollHeight;
                break;

            case 'complete':
                isComplete = true;
                handleStreamComplete();
                break;

            case 'error':
                // 隐藏流式指示器
                streamingIndicator.style.display = 'none';

                // 移除光标
                if (cursor.parentNode) {
                    cursor.parentNode.removeChild(cursor);
                }

                // 重置界面状态：隐藏结果区域，显示占位符
                resultsDiv.style.display = 'none';
                if (placeholderDiv) placeholderDiv.style.display = 'flex';
                if (contextSection) contextSection.style.display = 'none';

                // 清空内容
                streamingContentDiv.innerHTML = '';
                contextContentDiv.innerHTML = '';
                contextCountSpan.textContent = '0';

                // 记录错误日志
                addLog(`AI分析失败: ${data.message}`, 'error');

                // 显示用户友好的错误提示
                alert(`AI分析失败: ${data.message}\n\n请检查配置或稍后重试。`);
                break;
        }
    }

    function handleStreamComplete() {
        // 隐藏流式指示器
        streamingIndicator.style.display = 'none';

        // 移除光标
        if (cursor.parentNode) {
            cursor.parentNode.removeChild(cursor);
        }

        // 检查上下文是否正确显示
        console.log('🔍 检查上下文显示状态...');
        console.log(`上下文数组长度: ${contexts.length}`);
        console.log(`上下文计数显示: ${contextCountSpan.textContent}`);
        console.log(`上下文部分可见性: ${contextSection ? contextSection.style.display : 'N/A'}`);

        // 如果有上下文但没有正确显示，尝试重新显示
        if (contexts.length > 0) {
            const currentCount = parseInt(contextCountSpan.textContent) || 0;
            if (currentCount === 0 || (contextSection && contextSection.style.display === 'none')) {
                console.warn('⚠️ 检测到上下文显示异常，尝试重新显示...');
                try {
                    displayContextReferences(contexts);
                    addLog('重新显示上下文引用', 'info');
                } catch (error) {
                    console.error('❌ 重新显示上下文失败:', error);
                }
            }
        }

        // 计算分析耗时
        aiAnalysisState.endTime = Date.now();
        aiAnalysisState.duration = ((aiAnalysisState.endTime - aiAnalysisState.startTime) / 1000).toFixed(1);

        // 更新标题显示耗时
        updateAnalysisResultTitle();

        // 检查AI响应内容是否为空
        if (!aiAnalysisState.rawContent || aiAnalysisState.rawContent.trim() === '') {
            console.warn('AI分析完成但内容为空');
            streamingContentDiv.innerHTML = `<div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                AI分析完成，但返回内容为空。请检查API配置或重试。
            </div>`;
            addLog('AI分析完成但返回内容为空', 'warning');
        } else {
            console.log(`AI分析完成，内容长度: ${aiAnalysisState.rawContent.length} 字符`);

            // 如果当前是Markdown视图，进行最终的完整解析
            if (aiAnalysisState.isMarkdownView) {
                try {
                    // 使用安全的markdown解析
                    const markdownHtml = safeMarkdownParse(aiAnalysisState.rawContent);
                    streamingContentDiv.innerHTML = markdownHtml;
                    // 代码高亮
                    streamingContentDiv.querySelectorAll('pre code').forEach((block) => {
                        try {
                            hljs.highlightElement(block);
                        } catch (highlightError) {
                            console.warn('代码高亮失败:', highlightError);
                        }
                    });
                    streamingContentDiv.classList.add('markdown-content');
                    console.log('最终Markdown解析成功');
                } catch (e) {
                    console.warn('最终Markdown解析失败:', e);
                    console.warn('原始内容长度:', aiAnalysisState.rawContent.length);
                    console.warn('内容开头:', aiAnalysisState.rawContent.substring(0, 200));
                    // 回退到纯文本
                    streamingContentDiv.textContent = aiAnalysisState.rawContent;
                    streamingContentDiv.classList.remove('markdown-content');

                    // 显示错误提示
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-warning mt-2';
                    errorDiv.innerHTML = `
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Markdown渲染失败</strong><br>
                        已切换到纯文本显示。您可以点击右上角的"纯文本"按钮手动切换视图。
                    `;
                    streamingContentDiv.appendChild(errorDiv);
                }
            }

            // 保存分析结果到历史记录（防止重复保存）
            if (!historySaved) {
                saveAnalysisToHistory(requestData, aiAnalysisState.rawContent, contexts);
                historySaved = true;
                console.log('AI分析结果已保存到历史记录');
            }

            addLog('AI分析流式响应完成', 'success');
        }

        // 最终状态检查
        console.log('🏁 AI分析完成状态检查:');
        console.log(`- AI内容长度: ${aiAnalysisState.rawContent.length}`);
        console.log(`- 上下文数量: ${contexts.length}`);
        console.log(`- 上下文显示: ${contextSection ? contextSection.style.display : 'N/A'}`);
    }
}

// 添加折叠按钮事件监听
document.addEventListener('DOMContentLoaded', function() {
    const contextReferences = document.getElementById('contextReferences');
    const contextToggleIcon = document.getElementById('contextToggleIcon');

    if (contextReferences && contextToggleIcon) {
        contextReferences.addEventListener('show.bs.collapse', function() {
            contextToggleIcon.style.transform = 'rotate(90deg)';
        });

        contextReferences.addEventListener('hide.bs.collapse', function() {
            contextToggleIcon.style.transform = 'rotate(0deg)';
        });
    }

    // 添加筛选相关的键盘快捷键
    document.addEventListener('keydown', function(e) {
        // 只在筛选面板展开时响应快捷键
        const filtersPanel = document.getElementById('resultsFilters');
        if (!filtersPanel || !filtersPanel.classList.contains('show')) return;

        // Ctrl + Enter: 应用筛选
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            applyResultsFilters();
        }

        // Ctrl + R: 清除筛选
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            clearResultsFilters();
        }

        // Escape: 关闭筛选面板
        if (e.key === 'Escape') {
            const collapseInstance = new bootstrap.Collapse(filtersPanel, {
                toggle: false
            });
            collapseInstance.hide();
        }
    });
});

function debugDatabase() {
    showLoading('正在检查数据库状态...');
    addLog('开始调试数据库...', 'info');

    fetch('/api/debug_database', {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        if (data.success) {
            const debugInfo = data.debug_info;
            addLog(`数据库调试完成`, 'success');

            // 显示调试信息
            let debugMessage = `数据库状态检查结果：\n\n`;
            debugMessage += `📊 总年报数量: ${debugInfo.total_reports}\n`;
            debugMessage += `🏢 总公司数量: ${debugInfo.total_companies}\n`;
            debugMessage += `📈 唯一股票代码数量: ${debugInfo.unique_stock_codes}\n\n`;

            if (debugInfo.stock_codes_list.length > 0) {
                debugMessage += `可用股票代码:\n${debugInfo.stock_codes_list.join(', ')}\n\n`;
            } else {
                debugMessage += `❌ 数据库中没有股票代码\n\n`;
            }

            if (debugInfo.sample_reports.length > 0) {
                debugMessage += `示例年报:\n`;
                debugInfo.sample_reports.forEach((report, index) => {
                    debugMessage += `${index + 1}. ${report.stock_code} - ${report.company_name || 'Unknown'} - ${report.file_name || 'Unknown'}\n`;
                });
            } else {
                debugMessage += `❌ 数据库中没有年报数据\n`;
            }

            alert(debugMessage);

            // 记录详细日志
            addLog(`📊 总年报: ${debugInfo.total_reports}, 总公司: ${debugInfo.total_companies}`, 'info');
            addLog(`📈 可用股票代码: ${debugInfo.stock_codes_list.join(', ')}`, 'info');

        } else {
            addLog(`数据库调试失败: ${data.message}`, 'error');
            alert(`数据库调试失败: ${data.message}`);
        }
    })
    .catch(error => {
        hideLoading();

        // 强制关闭弹窗（双重保险）
        setTimeout(() => {
            hideLoading();
        }, 100);

        addLog(`数据库调试请求失败: ${error.message}`, 'error');
        alert(`数据库调试请求失败: ${error.message}`);
    });
}

// 调试函数：检查AI分析上下文状态
function debugAiContextState() {
    console.log('🔧 AI分析上下文状态调试:');
    console.log('- aiContextData:', aiContextData);
    console.log('- contextSection 元素:', document.getElementById('contextSection'));
    console.log('- contextContent 元素:', document.getElementById('contextContent'));
    console.log('- contextCount 元素:', document.getElementById('contextCount'));

    const contextSection = document.getElementById('contextSection');
    const contextContent = document.getElementById('contextContent');
    const contextCount = document.getElementById('contextCount');

    if (contextSection) {
        console.log('- contextSection 可见性:', contextSection.style.display);
        console.log('- contextSection 内容长度:', contextSection.innerHTML.length);
    }

    if (contextContent) {
        console.log('- contextContent 内容长度:', contextContent.innerHTML.length);
        console.log('- contextContent 内容预览:', contextContent.innerHTML.substring(0, 200));
    }

    if (contextCount) {
        console.log('- contextCount 文本:', contextCount.textContent);
    }

    return {
        aiContextData,
        elements: {
            contextSection: contextSection ? 'found' : 'missing',
            contextContent: contextContent ? 'found' : 'missing',
            contextCount: contextCount ? 'found' : 'missing'
        },
        visibility: contextSection ? contextSection.style.display : 'N/A',
        contentLength: contextContent ? contextContent.innerHTML.length : 0,
        count: contextCount ? contextCount.textContent : 'N/A'
    };
}

// 暴露调试函数到全局作用域
window.debugAiContextState = debugAiContextState;

// AI分析上下文分页显示函数
function showAiContextPage(contextType, page) {
    const contexts = aiContextData[contextType];
    if (!contexts || contexts.length === 0) return;

    const pageSize = aiContextData.pageSize;
    const totalPages = Math.ceil(contexts.length / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, contexts.length);

    const containerId = `${contextType}-container`;
    const paginationId = `${contextType}-pagination`;

    // 显示当前页的上下文
    let contextHtml = '';
    for (let i = startIndex; i < endIndex; i++) {
        const context = contexts[i];
        let highlightedContext = context.context;

        if (contextType === 'withKeywords') {
            // 高亮关键词
            if (context.keywords_found && context.keywords_found.length > 0) {
                context.keywords_found.forEach(keyword => {
                    const regex = new RegExp(keyword, 'gi');
                    highlightedContext = highlightedContext.replace(regex, `<span class="keyword-highlight">${keyword}</span>`);
                });
            }

            contextHtml += `
                <div class="context-item border-success mb-3">
                    <div class="context-meta">
                        <strong>${context.stock_code}</strong> - ${context.company_name}
                        <span class="badge bg-primary ms-2">${context.related_party}</span>
                        ${context.keywords_found && context.keywords_found.length > 0 ?
                            context.keywords_found.map(kw => `<span class="badge bg-warning text-dark ms-1">${kw}</span>`).join('') :
                            ''}
                    </div>
                    <div class="context-text">
                        ${highlightedContext}
                    </div>
                </div>
            `;
        } else {
            // 高亮关联方名称
            const regex = new RegExp(context.related_party, 'gi');
            highlightedContext = highlightedContext.replace(regex, `<span class="bg-info text-white px-1 rounded">${context.related_party}</span>`);

            contextHtml += `
                <div class="context-item border-info mb-3">
                    <div class="context-meta">
                        <strong>${context.stock_code}</strong> - ${context.company_name}
                        <span class="badge bg-info ms-2">${context.related_party}</span>
                        <span class="badge bg-secondary ms-1">仅关联方</span>
                    </div>
                    <div class="context-text">
                        ${highlightedContext}
                    </div>
                </div>
            `;
        }
    }

    document.getElementById(containerId).innerHTML = contextHtml;

    // 生成分页控件
    let paginationHtml = '';
    if (totalPages > 1) {
        paginationHtml = '<nav><ul class="pagination pagination-sm justify-content-center mb-0">';

        // 上一页
        if (page > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showAiContextPage('${contextType}', ${page - 1}); return false;">上一页</a></li>`;
        }

        // 页码（显示当前页前后2页）
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(totalPages, page + 2);

        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showAiContextPage('${contextType}', 1); return false;">1</a></li>`;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'active' : '';
            paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="showAiContextPage('${contextType}', ${i}); return false;">${i}</a></li>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showAiContextPage('${contextType}', ${totalPages}); return false;">${totalPages}</a></li>`;
        }

        // 下一页
        if (page < totalPages) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="showAiContextPage('${contextType}', ${page + 1}); return false;">下一页</a></li>`;
        }

        paginationHtml += '</ul></nav>';

        // 添加页面信息
        paginationHtml += `<small class="text-muted text-center d-block mt-2">第 ${page} 页，共 ${totalPages} 页 (共 ${contexts.length} 条)</small>`;
    }

    document.getElementById(paginationId).innerHTML = paginationHtml;

    // 更新当前页码
    if (contextType === 'withKeywords') {
        aiContextData.currentWithKeywordsPage = page;
    } else {
        aiContextData.currentRelatedPartyPage = page;
    }
}

function showAllAiContexts(contextType) {
    const contexts = aiContextData[contextType];
    if (!contexts || contexts.length === 0) return;

    const containerId = `${contextType}-container`;
    const paginationId = `${contextType}-pagination`;

    // 显示所有上下文
    let contextHtml = '';
    contexts.forEach((context, index) => {
        let highlightedContext = context.context;

        if (contextType === 'withKeywords') {
            // 高亮关键词
            if (context.keywords_found && context.keywords_found.length > 0) {
                context.keywords_found.forEach(keyword => {
                    const regex = new RegExp(keyword, 'gi');
                    highlightedContext = highlightedContext.replace(regex, `<span class="keyword-highlight">${keyword}</span>`);
                });
            }

            contextHtml += `
                <div class="context-item border-success mb-3">
                    <div class="context-meta">
                        <strong>${context.stock_code}</strong> - ${context.company_name}
                        <span class="badge bg-primary ms-2">${context.related_party}</span>
                        ${context.keywords_found && context.keywords_found.length > 0 ?
                            context.keywords_found.map(kw => `<span class="badge bg-warning text-dark ms-1">${kw}</span>`).join('') :
                            ''}
                    </div>
                    <div class="context-text">
                        ${highlightedContext}
                    </div>
                </div>
            `;
        } else {
            // 高亮关联方名称
            const regex = new RegExp(context.related_party, 'gi');
            highlightedContext = highlightedContext.replace(regex, `<span class="bg-info text-white px-1 rounded">${context.related_party}</span>`);

            contextHtml += `
                <div class="context-item border-info mb-3">
                    <div class="context-meta">
                        <strong>${context.stock_code}</strong> - ${context.company_name}
                        <span class="badge bg-info ms-2">${context.related_party}</span>
                        <span class="badge bg-secondary ms-1">仅关联方</span>
                    </div>
                    <div class="context-text">
                        ${highlightedContext}
                    </div>
                </div>
            `;
        }
    });

    document.getElementById(containerId).innerHTML = contextHtml;
    document.getElementById(paginationId).innerHTML = `<small class="text-muted text-center d-block">显示全部 ${contexts.length} 条上下文</small>`;

    // 更新按钮状态
    updateAiContextButtonState(contextType, 'all');
}

function showPaginatedAiContexts(contextType) {
    const currentPage = contextType === 'withKeywords' ? aiContextData.currentWithKeywordsPage : aiContextData.currentRelatedPartyPage;
    showAiContextPage(contextType, currentPage);

    // 更新按钮状态
    updateAiContextButtonState(contextType, 'paginated');
}

function updateAiContextButtonState(contextType, mode) {
    const buttonGroup = document.querySelector(`#${contextType}-container`).closest('.context-section').querySelector('.btn-group');
    if (buttonGroup) {
        const buttons = buttonGroup.querySelectorAll('.btn');
        buttons.forEach(btn => btn.classList.remove('active'));

        if (mode === 'all') {
            buttons[0].classList.add('active'); // "显示全部"按钮
        } else {
            buttons[1].classList.add('active'); // "分页显示"按钮
        }
    }
}



function toggleMarkdownView() {
    const streamingContentDiv = document.getElementById('aiStreamingContent');
    const markdownIcon = document.getElementById('markdownIcon');
    const markdownText = document.getElementById('markdownText');

    if (aiAnalysisState.isMarkdownView) {
        // 切换到纯文本视图
        streamingContentDiv.textContent = aiAnalysisState.rawContent;
        streamingContentDiv.classList.remove('markdown-content');
        markdownIcon.className = 'bi bi-markdown';
        markdownText.textContent = 'Markdown';
        aiAnalysisState.isMarkdownView = false;
    } else {
        // 切换到Markdown视图
        if (aiAnalysisState.rawContent) {
            try {
                // 使用安全的markdown解析
                const markdownHtml = safeMarkdownParse(aiAnalysisState.rawContent);
                streamingContentDiv.innerHTML = markdownHtml;
                // 代码高亮
                streamingContentDiv.querySelectorAll('pre code').forEach((block) => {
                    try {
                        hljs.highlightElement(block);
                    } catch (highlightError) {
                        console.warn('代码高亮失败:', highlightError);
                    }
                });
                streamingContentDiv.classList.add('markdown-content');
                markdownIcon.className = 'bi bi-file-text';
                markdownText.textContent = '纯文本';
                aiAnalysisState.isMarkdownView = true;
            } catch (e) {
                console.warn('Markdown解析失败:', e);
                console.warn('原始内容长度:', aiAnalysisState.rawContent.length);
                alert('Markdown解析失败，内容可能包含不支持的格式。已保持纯文本显示。');
            }
        } else {
            alert('暂无内容可以转换为Markdown视图');
        }
    }
}

// 配置marked.js选项
if (typeof marked !== 'undefined') {
    marked.setOptions({
        breaks: true,        // 支持换行
        gfm: true,          // 支持GitHub风格的markdown
        sanitize: false,    // 不过度清理HTML
        smartLists: true,   // 智能列表
        smartypants: false, // 禁用智能标点，避免特殊字符问题
        xhtml: false        // 不使用XHTML
    });
}

// 清理和预处理Markdown内容
function cleanMarkdownContent(content) {
    if (!content) return '';

    try {
        // 移除可能导致解析问题的字符
        let cleaned = content
            // 移除零宽字符和控制字符
            .replace(/[\u200B-\u200D\uFEFF\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, '')
            // 修复常见的markdown格式问题
            .replace(/\*\*\*([^*]+)\*\*\*/g, '**$1**') // 三个星号改为两个
            .replace(/___([^_]+)___/g, '__$1__') // 三个下划线改为两个
            // 修复表格格式
            .replace(/\|\s*\|\s*\|/g, '| | |') // 修复空表格单元格
            .replace(/\|([^|\n]*)\|/g, (match, content) => {
                // 确保表格单元格内容不包含换行
                return '|' + content.replace(/\n/g, ' ') + '|';
            })
            // 修复代码块
            .replace(/```(\w*)\n?([^`]*?)```/g, (match, lang, code) => {
                return '```' + (lang || '') + '\n' + code.trim() + '\n```';
            })
            // 修复行内代码
            .replace(/`([^`\n]+)`/g, '`$1`')
            // 修复链接格式
            .replace(/\[([^\]]*)\]\(([^)]*)\)/g, (match, text, url) => {
                if (!url || (!url.startsWith('http') && !url.startsWith('#') && !url.startsWith('/'))) {
                    return text; // 如果链接格式不正确，只保留文本
                }
                return match;
            })
            // 修复标题格式
            .replace(/^(#{1,6})\s*(.*)$/gm, '$1 $2')
            // 移除可能的恶意HTML标签
            .replace(/<script[^>]*>.*?<\/script>/gi, '')
            .replace(/<style[^>]*>.*?<\/style>/gi, '')
            .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
            // 修复列表格式
            .replace(/^(\s*)[*+-]\s+/gm, '$1- ')
            .replace(/^(\s*)\d+\.\s+/gm, '$11. ');

        return cleaned;
    } catch (e) {
        console.warn('内容清理失败:', e);
        return content; // 如果清理失败，返回原内容
    }
}

// 安全的markdown解析函数
function safeMarkdownParse(content) {
    try {
        if (!content || typeof content !== 'string') {
            return '';
        }

        // 清理内容
        const cleanedContent = cleanMarkdownContent(content);

        // 分段解析，避免大内容导致的问题
        if (cleanedContent.length > 50000) {
            console.warn('内容过长，进行分段处理');
            const chunks = [];
            const chunkSize = 10000;
            for (let i = 0; i < cleanedContent.length; i += chunkSize) {
                const chunk = cleanedContent.substring(i, i + chunkSize);
                try {
                    chunks.push(marked.parse(chunk));
                } catch (chunkError) {
                    console.warn(`分段 ${i}-${i + chunkSize} 解析失败:`, chunkError);
                    chunks.push('<pre>' + chunk + '</pre>'); // 回退到预格式化文本
                }
            }
            return chunks.join('');
        } else {
            return marked.parse(cleanedContent);
        }
    } catch (error) {
        console.error('Markdown解析完全失败:', error);
        // 最后的回退：返回预格式化的纯文本
        return '<pre>' + (content || '').replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>';
    }
}

// 更新AI分析结果标题，显示耗时
function updateAnalysisResultTitle() {
    const titleElement = document.querySelector('#aiAnalysisResults h6');
    if (titleElement && aiAnalysisState.duration > 0) {
        titleElement.innerHTML = `
            <i class="bi bi-lightbulb"></i> AI分析结果
            <span class="badge bg-info ms-2">
                <i class="bi bi-clock"></i> 耗时 ${aiAnalysisState.duration}秒
            </span>
        `;
    }
}

// 重置AI分析状态
function resetAiAnalysisState() {
    aiAnalysisState.rawContent = '';
    aiAnalysisState.markdownContent = '';
    aiAnalysisState.isMarkdownView = true;  // 默认启用markdown视图
    aiAnalysisState.startTime = null;
    aiAnalysisState.endTime = null;
    aiAnalysisState.duration = 0;

    // 重置Markdown按钮状态为markdown模式
    const markdownIcon = document.getElementById('markdownIcon');
    const markdownText = document.getElementById('markdownText');
    if (markdownIcon && markdownText) {
        markdownIcon.className = 'bi bi-file-text';
        markdownText.textContent = '纯文本';
    }

    // 重置标题
    const titleElement = document.querySelector('#aiAnalysisResults .card-header h6');
    if (titleElement) {
        titleElement.innerHTML = '<i class="bi bi-robot"></i> AI分析结果';
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化AI分析界面...');

    // 确保AI分析界面初始状态正确
    const resultsDiv = document.getElementById('aiAnalysisResults');
    const placeholderDiv = document.getElementById('aiAnalysisPlaceholder');
    const contextSection = document.getElementById('contextSection');
    const streamingIndicator = document.getElementById('aiStreamingIndicator');

    // 设置初始显示状态
    if (resultsDiv) {
        resultsDiv.style.display = 'none';
        console.log('隐藏AI分析结果区域');
    }

    if (placeholderDiv) {
        placeholderDiv.style.display = 'flex';
        console.log('显示占位符');
    }

    if (contextSection) {
        contextSection.style.display = 'none';
        console.log('隐藏上下文区域');
    }

    if (streamingIndicator) {
        streamingIndicator.style.display = 'none';
        console.log('隐藏流式指示器');
    }

    // 重置AI分析状态
    resetAiAnalysisState();

    // 清空内容
    const streamingContent = document.getElementById('aiStreamingContent');
    const contextContent = document.getElementById('contextContent');
    const contextCount = document.getElementById('contextCount');

    if (streamingContent) streamingContent.innerHTML = '';
    if (contextContent) contextContent.innerHTML = '';
    if (contextCount) contextCount.textContent = '0';

    console.log('AI分析界面初始化完成');

    // 加载历史记录
    loadAnalysisHistory();
});

// AI分析历史记录功能
function saveAnalysisToHistory(requestData, analysisResult, contexts) {
    // 检查是否有相同的分析结果（基于时间戳和内容）
    const currentTime = Date.now();
    const recentHistory = analysisHistoryState.histories.find(h => {
        const timeDiff = currentTime - parseInt(h.id);
        return timeDiff < 5000 && // 5秒内
               h.analysisResult === analysisResult && // 相同内容
               JSON.stringify(h.requestData) === JSON.stringify(requestData); // 相同请求
    });

    if (recentHistory) {
        console.log('检测到重复的分析结果，跳过保存');
        return;
    }

    const historyItem = {
        id: currentTime.toString(),
        timestamp: new Date().toISOString(),
        requestData: requestData,
        analysisResult: analysisResult,
        contexts: contexts || [],
        title: generateHistoryTitle(requestData),
        summary: generateHistorySummary(analysisResult)
    };

    // 添加到历史记录数组开头
    analysisHistoryState.histories.unshift(historyItem);

    // 限制历史记录数量（最多保存50条）
    if (analysisHistoryState.histories.length > 50) {
        analysisHistoryState.histories = analysisHistoryState.histories.slice(0, 50);
    }

    // 保存到localStorage
    try {
        localStorage.setItem('aiAnalysisHistory', JSON.stringify(analysisHistoryState.histories));
        console.log('AI分析结果已保存到历史记录');
    } catch (e) {
        console.warn('保存历史记录失败:', e);
    }
}

function generateHistoryTitle(requestData) {
    let stockCodes = '未知';
    let moreCount = '';

    if (requestData.stock_codes) {
        // 确保stock_codes是数组格式
        let codesArray = Array.isArray(requestData.stock_codes) ?
            requestData.stock_codes :
            requestData.stock_codes.split('\n').filter(code => code.trim());

        if (codesArray.length > 0) {
            stockCodes = codesArray.slice(0, 3).join(', ');
            moreCount = codesArray.length > 3 ? ` 等${codesArray.length}个` : '';
        }
    }

    return `${stockCodes}${moreCount} - AI分析`;
}

function generateHistorySummary(analysisResult) {
    if (!analysisResult) return '无分析结果';

    // 提取前100个字符作为摘要
    const summary = analysisResult.replace(/[#*`\n]/g, ' ').trim();
    return summary.length > 100 ? summary.substring(0, 100) + '...' : summary;
}

function loadAnalysisHistory() {
    try {
        const saved = localStorage.getItem('aiAnalysisHistory');
        if (saved) {
            analysisHistoryState.histories = JSON.parse(saved);
            console.log(`加载了 ${analysisHistoryState.histories.length} 条历史记录`);
        }
    } catch (e) {
        console.warn('加载历史记录失败:', e);
        analysisHistoryState.histories = [];
    }
}

function showAnalysisHistory() {
    // 显示历史记录模态框
    const modal = new bootstrap.Modal(document.getElementById('analysisHistoryModal'));
    modal.show();

    // 刷新历史记录列表
    refreshHistoryList();

    // 清空详情区域
    document.getElementById('historyDetail').innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="bi bi-arrow-left" style="font-size: 2rem; opacity: 0.3;"></i>
            <p class="mt-3 mb-0">请从左侧选择一个历史记录查看详情</p>
        </div>
    `;

    // 重置选中状态
    analysisHistoryState.selectedHistoryId = null;
    document.getElementById('loadHistoryBtn').disabled = true;
}

// 辅助函数：安全地获取股票代码数量
function getStockCodesCount(stockCodes) {
    if (!stockCodes) return 0;
    if (Array.isArray(stockCodes)) return stockCodes.length;
    if (typeof stockCodes === 'string') {
        return stockCodes.split('\n').filter(code => code.trim()).length;
    }
    return 0;
}

function refreshHistoryList() {
    const historyListDiv = document.getElementById('historyList');

    if (analysisHistoryState.histories.length === 0) {
        historyListDiv.innerHTML = `
            <div class="history-empty">
                <i class="bi bi-clock-history"></i>
                <p class="mb-0">暂无历史记录</p>
                <small class="text-muted">完成AI分析后，结果会自动保存到这里</small>
            </div>
        `;
        return;
    }

    let html = '';
    analysisHistoryState.histories.forEach((history, index) => {
        const date = new Date(history.timestamp);
        const timeStr = date.toLocaleString('zh-CN');
        const isActive = history.id === analysisHistoryState.selectedHistoryId ? 'active' : '';

        html += `
            <div class="history-item ${isActive}" onclick="selectHistory('${history.id}')">
                <div class="history-meta">
                    <i class="bi bi-clock"></i> ${timeStr}
                </div>
                <div class="history-title">${history.title}</div>
                <div class="history-summary">${history.summary}</div>
                <div class="history-tags">
                    <span class="history-tag">
                        <i class="bi bi-graph-up"></i> ${getStockCodesCount(history.requestData.stock_codes)}个股票
                    </span>
                    <span class="history-tag">
                        <i class="bi bi-quote"></i> ${history.contexts ? history.contexts.length : 0}个上下文
                    </span>
                </div>
            </div>
        `;
    });

    historyListDiv.innerHTML = html;
}

function selectHistory(historyId) {
    analysisHistoryState.selectedHistoryId = historyId;

    // 更新列表中的选中状态
    document.querySelectorAll('.history-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.closest('.history-item').classList.add('active');

    // 显示历史记录详情
    const history = analysisHistoryState.histories.find(h => h.id === historyId);
    if (history) {
        displayHistoryDetail(history);
        document.getElementById('loadHistoryBtn').disabled = false;
    }
}

function displayHistoryDetail(history) {
    const detailDiv = document.getElementById('historyDetail');
    const date = new Date(history.timestamp);
    const timeStr = date.toLocaleString('zh-CN');

    // 安全地处理数组数据
    const getArrayDisplay = (data) => {
        if (!data) return '无';
        if (Array.isArray(data)) return data.join(', ');
        if (typeof data === 'string') return data.split('\n').filter(item => item.trim()).join(', ');
        return '无';
    };

    let html = `
        <div class="analysis-meta">
            <h6><i class="bi bi-info-circle"></i> 分析信息</h6>
            <div class="meta-item">
                <span class="meta-label">分析时间：</span>${timeStr}
            </div>
            <div class="meta-item">
                <span class="meta-label">股票代码：</span>${getArrayDisplay(history.requestData.stock_codes)}
            </div>
            <div class="meta-item">
                <span class="meta-label">关键词：</span>${getArrayDisplay(history.requestData.keywords)}
            </div>
            <div class="meta-item">
                <span class="meta-label">关联方：</span>${getArrayDisplay(history.requestData.related_parties)}
            </div>
            <div class="meta-item">
                <span class="meta-label">上下文数量：</span>${history.contexts ? history.contexts.length : 0} 个
            </div>
        </div>

        <div class="analysis-content" id="historyAnalysisContent">
            ${history.analysisResult || '无分析结果'}
        </div>
    `;

    detailDiv.innerHTML = html;

    // 重置markdown状态
    analysisHistoryState.isHistoryMarkdownView = false;
    const markdownBtn = document.getElementById('toggleHistoryMarkdown');
    markdownBtn.innerHTML = '<i class="bi bi-markdown"></i> Markdown';
}

function toggleHistoryMarkdownView() {
    const contentDiv = document.getElementById('historyAnalysisContent');
    const markdownBtn = document.getElementById('toggleHistoryMarkdown');

    if (!analysisHistoryState.selectedHistoryId) return;

    const history = analysisHistoryState.histories.find(h => h.id === analysisHistoryState.selectedHistoryId);
    if (!history || !history.analysisResult) return;

    if (analysisHistoryState.isHistoryMarkdownView) {
        // 切换到纯文本视图
        contentDiv.textContent = history.analysisResult;
        contentDiv.classList.remove('markdown-content');
        markdownBtn.innerHTML = '<i class="bi bi-markdown"></i> Markdown';
        analysisHistoryState.isHistoryMarkdownView = false;
    } else {
        // 切换到Markdown视图
        try {
            const markdownHtml = marked.parse(history.analysisResult);
            contentDiv.innerHTML = markdownHtml;
            // 代码高亮
            contentDiv.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
            contentDiv.classList.add('markdown-content');
            markdownBtn.innerHTML = '<i class="bi bi-file-text"></i> 纯文本';
            analysisHistoryState.isHistoryMarkdownView = true;
        } catch (e) {
            console.warn('历史记录Markdown解析失败:', e);
            alert('Markdown解析失败，请检查内容格式');
        }
    }
}

function loadHistoryToMain() {
    if (!analysisHistoryState.selectedHistoryId) return;

    const history = analysisHistoryState.histories.find(h => h.id === analysisHistoryState.selectedHistoryId);
    if (!history) return;

    // 关闭历史记录模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('analysisHistoryModal'));
    modal.hide();

    // 加载数据到主界面
    const requestData = history.requestData;

    // 安全地处理数组数据并填充表单
    const setFormValue = (elementId, data) => {
        const element = document.getElementById(elementId);
        if (!element) return;

        if (!data) {
            element.value = '';
            return;
        }

        if (Array.isArray(data)) {
            element.value = data.join('\n');
        } else if (typeof data === 'string') {
            element.value = data;
        } else {
            element.value = '';
        }
    };

    // 填充表单数据
    setFormValue('aiStockCodes', requestData.stock_codes);
    setFormValue('aiKeywords', requestData.keywords);
    setFormValue('aiRelatedParties', requestData.related_parties);
    setFormValue('aiPrompt', requestData.prompt);

    // 显示分析结果
    const resultsDiv = document.getElementById('aiAnalysisResults');
    const placeholderDiv = document.getElementById('aiAnalysisPlaceholder');
    const streamingContentDiv = document.getElementById('aiStreamingContent');

    if (placeholderDiv) placeholderDiv.style.display = 'none';
    resultsDiv.style.display = 'block';

    // 设置分析结果内容
    aiAnalysisState.rawContent = history.analysisResult || '';
    streamingContentDiv.textContent = history.analysisResult || '无分析结果';
    streamingContentDiv.classList.remove('markdown-content');

    // 显示上下文
    if (history.contexts && history.contexts.length > 0) {
        displayContextReferences(history.contexts);
    }

    addLog(`已加载历史记录: ${history.title}`, 'info');
}

function clearAnalysisHistory() {
    if (analysisHistoryState.histories.length === 0) {
        alert('没有历史记录需要清空');
        return;
    }

    if (confirm(`确定要清空所有 ${analysisHistoryState.histories.length} 条历史记录吗？此操作不可恢复。`)) {
        analysisHistoryState.histories = [];
        analysisHistoryState.selectedHistoryId = null;

        // 清空localStorage
        try {
            localStorage.removeItem('aiAnalysisHistory');
        } catch (e) {
            console.warn('清空历史记录失败:', e);
        }

        // 刷新界面
        refreshHistoryList();
        document.getElementById('historyDetail').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-arrow-left" style="font-size: 2rem; opacity: 0.3;"></i>
                <p class="mt-3 mb-0">请从左侧选择一个历史记录查看详情</p>
            </div>
        `;
        document.getElementById('loadHistoryBtn').disabled = true;

        addLog('历史记录已清空', 'info');
    }
}

// 获取可用模型列表
function getAvailableModels(silent = false) {
    const getModelsBtn = document.getElementById('getModelsBtn');
    const modelSelect = document.getElementById('openaiModel');

    // 显示加载状态
    const originalText = getModelsBtn.innerHTML;
    getModelsBtn.disabled = true;
    getModelsBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 获取中...';

    if (!silent) {
        addLog('开始获取可用模型列表（使用服务器配置）...', 'info');
    } else {
        console.log('静默获取可用模型列表（使用服务器配置）...');
    }

    // 使用服务器默认配置获取模型
    const requestData = {};

    fetch('/api/get_models', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        getModelsBtn.disabled = false;
        getModelsBtn.innerHTML = originalText;

        if (data.success) {
            // 保存当前选中的模型
            const currentModel = modelSelect.value;

            // 清空现有选项
            modelSelect.innerHTML = '';

            // 只显示从API获取的模型
            if (data.models && data.models.length > 0) {
                // 添加从API获取的模型（初始状态不显示可用性）
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name || model.id;
                    option.setAttribute('data-checking', 'true'); // 标记正在检测
                    modelSelect.appendChild(option);
                });

                // 设置默认选中的模型为claude-sonnet-4（如果存在）
                const defaultModel = 'claude-sonnet-4';
                const foundDefaultOption = Array.from(modelSelect.options).find(opt => opt.value === defaultModel);
                if (foundDefaultOption) {
                    modelSelect.value = defaultModel;
                } else if (currentModel) {
                    // 如果没有找到默认模型，尝试恢复之前选中的模型
                    const foundCurrentOption = Array.from(modelSelect.options).find(opt => opt.value === currentModel);
                    if (foundCurrentOption) {
                        modelSelect.value = currentModel;
                    }
                }

                if (!silent) {
                    addLog(`成功获取到 ${data.models.length} 个服务器模型，正在检测可用性...`, 'success');
                    alert(`成功获取到 ${data.models.length} 个服务器模型！正在检测可用性...`);
                } else {
                    console.log(`静默获取成功：${data.models.length} 个服务器模型，开始检测可用性`);
                    // 更新提示信息
                    const hintElement = document.getElementById('modelSelectHint');
                    if (hintElement) {
                        hintElement.innerHTML = `<i class="bi bi-arrow-clockwise spin text-primary"></i> 已获取到 ${data.models.length} 个模型，正在检测可用性...`;
                        hintElement.className = 'text-info';
                    }
                }

                // 开始多线程检测模型可用性
                checkModelsAvailability(data.models, silent);

            } else {
                // 如果没有获取到模型，添加一个默认选项
                const defaultOption = document.createElement('option');
                defaultOption.value = 'claude-sonnet-4';
                defaultOption.textContent = 'Claude Sonnet 4 (默认)';
                modelSelect.appendChild(defaultOption);

                if (!silent) {
                    addLog('未获取到模型列表，使用默认模型', 'warning');
                    alert('未获取到模型列表，已设置为默认模型');
                } else {
                    console.log('静默获取：未获取到模型列表，使用默认模型');
                    // 更新提示信息
                    const hintElement = document.getElementById('modelSelectHint');
                    if (hintElement) {
                        hintElement.innerHTML = '未获取到模型列表，使用默认模型';
                        hintElement.className = 'text-warning';
                    }
                }
            }
        } else {
            if (!silent) {
                addLog(`获取模型列表失败: ${data.message}`, 'error');
                alert(`获取模型列表失败：${data.message}`);
            } else {
                console.log(`静默获取失败: ${data.message}`);
                // 恢复提示信息
                const hintElement = document.getElementById('modelSelectHint');
                if (hintElement) {
                    hintElement.innerHTML = '使用服务器配置的API，点击"获取模型"动态加载可用模型';
                    hintElement.className = 'text-muted';
                }
            }
        }
    })
    .catch(error => {
        // 恢复按钮状态
        getModelsBtn.disabled = false;
        getModelsBtn.innerHTML = originalText;

        if (!silent) {
            addLog(`获取模型列表请求失败: ${error.message}`, 'error');
            alert(`获取模型列表请求失败：${error.message}`);
        } else {
            console.log(`静默获取请求失败: ${error.message}`);
            // 恢复提示信息
            const hintElement = document.getElementById('modelSelectHint');
            if (hintElement) {
                hintElement.innerHTML = '使用服务器配置的API，点击"获取模型"动态加载可用模型';
                hintElement.className = 'text-muted';
            }
        }
    });
}

// 标记是否已经自动获取过模型
let hasAutoFetchedModels = false;

// 自动获取模型（仅首次进入时）
function autoGetModelsOnFirstOpen() {
    if (hasAutoFetchedModels) {
        console.log('已经自动获取过模型，跳过');
        return;
    }

    console.log('首次进入AI分析窗口，自动获取可用模型...');
    hasAutoFetchedModels = true;

    // 更新提示信息
    const hintElement = document.getElementById('modelSelectHint');
    if (hintElement) {
        hintElement.innerHTML = '<i class="bi bi-arrow-clockwise spin text-primary"></i> 正在自动获取服务器可用模型...';
        hintElement.className = 'text-info';
    }

    // 调用获取模型函数，但不显示用户提示
    getAvailableModels(true); // 传递silent参数
}

// 检查是否需要自动获取模型（页面加载时调用）
function checkAutoFetchModels() {
    // 检查模型选择框是否只有默认选项
    const modelSelect = document.getElementById('openaiModel');
    if (!modelSelect) return;

    const options = modelSelect.options;
    const hasOnlyDefaultOption = options.length <= 1; // 现在默认只有1个选项

    // 如果只有默认选项且还没有自动获取过，则自动获取
    if (hasOnlyDefaultOption && !hasAutoFetchedModels) {
        console.log('检测到只有默认模型选项，自动获取服务器可用模型...');
        setTimeout(() => {
            autoGetModelsOnFirstOpen();
        }, 1000); // 延迟1秒确保页面完全加载
    }
}

// 多线程检测模型可用性
function checkModelsAvailability(models, silent = false) {
    const modelSelect = document.getElementById('openaiModel');
    let completedChecks = 0;
    let availableCount = 0;

    console.log(`开始检测 ${models.length} 个模型的可用性...`);

    // 并发检测所有模型
    const checkPromises = models.map(model => {
        return checkSingleModelAvailability(model.id)
            .then(result => {
                // 更新对应的option元素
                const option = Array.from(modelSelect.options).find(opt => opt.value === model.id);
                if (option) {
                    option.removeAttribute('data-checking');
                    if (result.available) {
                        option.textContent = `${model.name || model.id} (可用)`;
                        option.setAttribute('data-available', 'true');
                        option.style.color = '#198754'; // Bootstrap success color
                        availableCount++;
                    } else {
                        option.textContent = `${model.name || model.id} (不可用)`;
                        option.setAttribute('data-available', 'false');
                        option.style.color = '#dc3545'; // Bootstrap danger color
                    }
                }

                completedChecks++;
                console.log(`模型 ${model.id} 检测完成: ${result.available ? '可用' : '不可用'} (${completedChecks}/${models.length})`);

                // 如果所有检测都完成了
                if (completedChecks === models.length) {
                    const hintElement = document.getElementById('modelSelectHint');
                    if (hintElement && silent) {
                        hintElement.innerHTML = `<i class="bi bi-check-circle text-success"></i> 检测完成：${availableCount}/${models.length} 个模型可用`;
                        hintElement.className = 'text-success';
                    }

                    if (!silent) {
                        addLog(`模型可用性检测完成：${availableCount}/${models.length} 个模型可用`, 'info');
                    }

                    console.log(`所有模型检测完成：${availableCount}/${models.length} 个模型可用`);
                }

                return result;
            })
            .catch(error => {
                console.error(`检测模型 ${model.id} 时出错:`, error);

                // 标记为检测失败
                const option = Array.from(modelSelect.options).find(opt => opt.value === model.id);
                if (option) {
                    option.removeAttribute('data-checking');
                    option.textContent = `${model.name || model.id} (检测失败)`;
                    option.setAttribute('data-available', 'unknown');
                    option.style.color = '#6c757d'; // Bootstrap secondary color
                }

                completedChecks++;
                return { available: false, error: error.message };
            });
    });

    // 等待所有检测完成（可选，用于调试）
    Promise.all(checkPromises).then(results => {
        console.log('所有模型检测结果:', results);
    });
}

// 检测单个模型可用性
function checkSingleModelAvailability(modelId) {
    return fetch('/api/check_model_availability', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model_id: modelId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            return {
                available: data.available,
                message: data.message
            };
        } else {
            return {
                available: false,
                message: data.message || '检测失败'
            };
        }
    });
}

// 初始化获取模型按钮状态
document.addEventListener('DOMContentLoaded', function() {
    const getModelsBtn = document.getElementById('getModelsBtn');

    // 使用服务器配置，始终启用获取模型按钮
    if (getModelsBtn) {
        getModelsBtn.disabled = false;
    }

    // 检查是否需要自动获取模型
    checkAutoFetchModels();
});

