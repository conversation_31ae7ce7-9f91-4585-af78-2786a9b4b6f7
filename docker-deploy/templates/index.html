<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巨潮资讯网年报爬虫工具 - 网页版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <!-- 代码高亮库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
    <style>
        /* 协同创新详情样式 */
        .innovation-details .context-item {
            transition: all 0.3s ease;
        }
        .innovation-details .context-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .innovation-details .context-content {
            line-height: 1.6;
            font-size: 0.95em;
        }
        .innovation-details .text-content {
            max-height: 200px;
            overflow-y: auto;
        }
        .innovation-details .badge {
            margin: 2px;
        }

        /* AI流式响应样式 */
        #aiStreamingContent {
            min-height: 100px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
        }

        /* AI分析结果markdown样式优化 */
        #aiStreamingContent h1,
        #aiStreamingContent h2,
        #aiStreamingContent h3,
        #aiStreamingContent h4,
        #aiStreamingContent h5,
        #aiStreamingContent h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        #aiStreamingContent p {
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        #aiStreamingContent ul,
        #aiStreamingContent ol {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
        }

        #aiStreamingContent li {
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }

        #aiStreamingContent blockquote {
            margin: 0.5rem 0;
            padding: 0.5rem 1rem;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }

        #aiStreamingContent code {
            background-color: #f8f9fa;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.9em;
        }

        #aiStreamingContent pre {
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin: 0.5rem 0;
            overflow-x: auto;
        }

        /* Markdown内容专用样式 - 更紧凑的间距 */
        .markdown-content {
            line-height: 1.4 !important;
        }

        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin-top: 0.8rem !important;
            margin-bottom: 0.4rem !important;
            line-height: 1.2 !important;
        }

        .markdown-content h1 {
            font-size: 1.5rem;
        }

        .markdown-content h2 {
            font-size: 1.3rem;
        }

        .markdown-content h3 {
            font-size: 1.1rem;
        }

        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            font-size: 1rem;
        }

        .markdown-content p {
            margin-bottom: 0.4rem !important;
            line-height: 1.4 !important;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 0.4rem !important;
            margin-top: 0.2rem !important;
            padding-left: 1.2rem !important;
        }

        .markdown-content li {
            margin-bottom: 0.15rem !important;
            line-height: 1.3 !important;
        }

        .markdown-content blockquote {
            margin: 0.4rem 0 !important;
            padding: 0.4rem 0.8rem !important;
            border-left: 3px solid #007bff;
            background-color: #f8f9fa;
            font-style: italic;
        }

        .markdown-content code {
            background-color: #f1f3f4;
            padding: 0.1rem 0.2rem !important;
            border-radius: 0.2rem;
            font-size: 0.85em;
            color: #d63384;
        }

        .markdown-content pre {
            background-color: #f8f9fa;
            padding: 0.4rem !important;
            border-radius: 0.25rem;
            margin: 0.4rem 0 !important;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }

        .markdown-content pre code {
            background-color: transparent !important;
            padding: 0 !important;
            color: inherit;
        }

        .markdown-content table {
            margin: 0.4rem 0 !important;
            border-collapse: collapse;
            width: 100%;
        }

        .markdown-content th,
        .markdown-content td {
            border: 1px solid #dee2e6;
            padding: 0.3rem 0.5rem !important;
            text-align: left;
        }

        .markdown-content th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .markdown-content hr {
            margin: 0.6rem 0 !important;
            border: none;
            border-top: 1px solid #dee2e6;
        }

        /* 强调文本样式 */
        .markdown-content strong {
            font-weight: 600;
            color: #212529;
        }

        .markdown-content em {
            font-style: italic;
            color: #6c757d;
        }

        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #007bff;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .context-item {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 15px;
        }

        .context-item.border-success {
            border-left-color: #28a745;
        }

        .context-item.border-info {
            border-left-color: #17a2b8;
        }

        .context-meta {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .context-text {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .keyword-highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 折叠按钮动画 */
        #contextToggleIcon {
            transition: transform 0.3s ease;
        }

        .collapsed #contextToggleIcon {
            transform: rotate(0deg);
        }

        .not-collapsed #contextToggleIcon {
            transform: rotate(90deg);
        }

        /* 筛选面板样式 */
        #resultsFilters .card-body {
            background-color: #f8f9fa;
        }

        #resultsFilters select[multiple] {
            min-height: 80px;
        }

        #resultsFilterStatus {
            padding: 8px 12px;
            background-color: #e3f2fd;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }

        .count-zero {
            color: #6c757d;
            background-color: #f8f9fa;
        }

        .count-high {
            color: #155724;
            background-color: #d4edda;
            font-weight: bold;
        }

        .count-medium {
            color: #856404;
            background-color: #fff3cd;
            font-weight: 600;
        }

        .keyword-cell {
            font-weight: 600;
            color: #495057;
        }

        .count-cell {
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 头部 -->
        <div class="row">
            <div class="col-12">
                <div class="header-section">
                    <h1 class="text-center mb-4">
                        <i class="bi bi-building"></i> 巨潮资讯网年报爬虫工具
                        <small class="text-muted">网页版</small>
                    </h1>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 控制面板</h5>
                    </div>
                    <div class="card-body">
                        <!-- 数据源选择 -->
                        <div class="mb-3">
                            <label class="form-label"><i class="bi bi-database"></i> 数据源选择</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="dataSource" id="onlineSource" value="online" checked>
                                <label class="form-check-label" for="onlineSource">
                                    在线下载PDF
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="dataSource" id="localSource" value="local">
                                <label class="form-check-label" for="localSource">
                                    使用本地数据库
                                </label>
                            </div>
                        </div>

                        <!-- 股票代码输入 -->
                        <div class="mb-3">
                            <label for="stockCodes" class="form-label">
                                <i class="bi bi-graph-up"></i> 股票代码 (每行一个)
                            </label>
                            <textarea class="form-control" id="stockCodes" rows="4" placeholder="300454&#10;300504&#10;300514">300454
300504
300514</textarea>
                        </div>

                        <!-- 搜索关键字 -->
                        <div class="mb-3" id="searchKeywordGroup">
                            <label for="searchKeyword" class="form-label">
                                <i class="bi bi-search"></i> 搜索关键字
                            </label>
                            <input type="text" class="form-control" id="searchKeyword" value="年度报告" placeholder="年度报告">
                        </div>

                        <!-- 在线下载时间范围 -->
                        <div class="row mb-3" id="dateRangeGroup">
                            <div class="col-12 mb-2">
                                <label class="form-label">
                                    <i class="bi bi-calendar-range"></i> 公告发布时间范围
                                </label>
                            </div>
                            <div class="col-6">
                                <label for="startDate" class="form-label small">开始日期</label>
                                <input type="date" class="form-control form-control-sm" id="startDate" value="2025-01-01">
                            </div>
                            <div class="col-6">
                                <label for="endDate" class="form-label small">结束日期</label>
                                <input type="date" class="form-control form-control-sm" id="endDate" value="2025-12-31">
                            </div>
                        </div>



                        <!-- 统计关键词 -->
                        <div class="mb-3">
                            <label for="keywords" class="form-label">
                                <i class="bi bi-tags"></i> 统计关键词 (每行一个)
                            </label>
                            <textarea class="form-control" id="keywords" rows="6" placeholder="请输入创新关键词">协同创新
合资设立
合作开发
合作研发
共同开发
联合研发
战略合作
整合资源
共同研发
个性化需求
客户合作
消费者合作
用户合作
协同开发
客户需求
技术交流
战略合作协议
产学研
联合开发
项目合作
协同创新
合作创新
战略联盟
研发联盟
开放式创新
研究院
技术合作
校企合作
联合申报
研发中心
博士后工作站
专家工作站
技术中心
科技计划立项
国家重点研发计划
技术服务
用户互动
客户需要
客户满意度
贴合顾客需求
围绕客户
消费者需求
合作协议
许可协议
合作项目
合资公司
消费者中心
资源共享
知识共享
知识溢出
信息共享
共享平台
价值共创
用户共创
资源互补
联合推出
联合生产
ODM
合作设立
研究所
共享创新
共同研制
协同效应
战略协作
资源整合
共同探索
客户偏好
研究平台
用户偏好
协同发展
共同布局
战略交流
政产学研
科研合作
融通创新
客户喜好
用户喜好
校企联合
共同申报
研究中心
科技战略联盟
研发机构
客户至上
消费者至上
合营公司
客户中心
研发共享
要素流动
资源充分结合
加强与企业的合作
战略框架协议
联盟
工作站
研究机构
高校机构
科研单位
用户导流
消费者调研
客户反馈
合作框架协议
协作研发
研发协同
创新协作
技术联盟
科研机构
联合实验室
共建实验室
科技园区
共建研发基地
用户需求
顾客需求
顾客至上
以客户为中心
以消费者为中心
合资企业
用户中心
资源协同
知识协同
要素共享
用户平台
要素互通</textarea>
                        </div>

                        <!-- 关联方分析 -->
                        <div class="mb-3">
                            <label for="relatedParties" class="form-label">
                                <i class="bi bi-people"></i> 关联方分析 (每行一个，可选)
                            </label>
                            <textarea class="form-control" id="relatedParties" rows="3" placeholder="华为技术有限公司&#10;腾讯科技&#10;阿里巴巴"></textarea>
                            <small class="text-muted">💡 输入供应商、经销商等关联方名称</small>
                        </div>

                        <!-- 上下文设置 -->
                        <div class="mb-3">
                            <label for="contextLength" class="form-label">
                                <i class="bi bi-text-paragraph"></i> 上下文片段长度
                            </label>
                            <select class="form-select" id="contextLength">
                                <option value="100" selected>简短 (100字符)</option>
                                <option value="200">标准 (200字符)</option>
                                <option value="300">详细 (300字符)</option>
                                <option value="500">完整 (500字符)</option>
                            </select>
                            <small class="text-muted">💡 设置查看关键词上下文时显示的内容长度</small>
                        </div>

                        <!-- 控制按钮 -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-primary" id="startAnalysisBtn">
                                <i class="bi bi-play-circle"></i> 开始分析
                            </button>
                            <button type="button" class="btn btn-danger" id="stopAnalysisBtn" disabled>
                                <i class="bi bi-stop-circle"></i> 停止分析
                            </button>
                            <button type="button" class="btn btn-info" id="keywordOnlyBtn">
                                <i class="bi bi-search"></i> 仅关键词分析
                            </button>
                            <button type="button" class="btn btn-secondary" id="importTxtBtn">
                                <i class="bi bi-upload"></i> 导入TXT文件
                            </button>
                            <button type="button" class="btn btn-warning" id="cleanDuplicatesBtn">
                                <i class="bi bi-trash"></i> 清理重复数据
                            </button>
                            <button type="button" class="btn btn-success" id="aiAnalysisBtn">
                                <i class="bi bi-robot"></i> AI分析
                            </button>
                            <button type="button" class="btn btn-outline-info" id="debugDatabaseBtn">
                                <i class="bi bi-bug"></i> 调试数据库
                            </button>
<!--                            <button type="button" class="btn btn-danger" onclick="forceCloseAllModals()" title="强制关闭所有弹窗">-->
<!--                                <i class="bi bi-x-circle"></i> 强制关闭弹窗-->
<!--                            </button>-->
                        </div>
                    </div>
                </div>

                <!-- 进度显示 -->
                <div class="card mt-3" id="progressCard" style="display: none;">
                    <div class="card-header">
                        <h6><i class="bi bi-clock"></i> 任务进度</h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-2">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-muted small">准备开始...</div>
                        <div id="taskStatus" class="mt-2">
                            <span class="badge bg-secondary">等待中</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示区域 -->
            <div class="col-md-8">
                <!-- 日志输出 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-terminal"></i> 日志输出</h5>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="clearLogBtn">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="logOutput" class="log-output">
                            <div class="log-entry">
                                <span class="timestamp">[系统启动]</span>
                                <span class="message">系统已就绪，请选择操作...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结果展示 -->
                <div class="card mt-3" id="resultsCard" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-table"></i> 分析结果</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-secondary me-2"
                                    data-bs-toggle="collapse" data-bs-target="#resultsFilters"
                                    aria-expanded="false" aria-controls="resultsFilters">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-success" id="exportResultsBtn" title="导出完整结果">
                                    <i class="bi bi-download"></i> 导出Excel
                                </button>
                                <button type="button" class="btn btn-success dropdown-toggle dropdown-toggle-split"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="visually-hidden">导出选项</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportResults('excel')">
                                        <i class="bi bi-file-earmark-excel"></i> Excel格式
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportResults('csv')">
                                        <i class="bi bi-file-earmark-text"></i> CSV格式
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportResults('summary')">
                                        <i class="bi bi-bar-chart"></i> 统计摘要
                                    </a></li>
                                </ul>
                            </div>
                            <button type="button" class="btn btn-sm btn-info" id="showSummaryBtn">
                                <i class="bi bi-bar-chart"></i> 统计摘要
                            </button>
                        </div>
                    </div>

                    <!-- 筛选面板 -->
                    <div class="collapse" id="resultsFilters">
                        <div class="card-body border-bottom">
                            <div class="row g-3">
                                <!-- 关键词筛选 -->
                                <div class="col-md-3">
                                    <label class="form-label">关键词筛选</label>
                                    <select id="resultsKeywordFilter" class="form-select form-select-sm" multiple>
                                        <!-- 动态填充关键词选项 -->
                                    </select>
                                    <small class="text-muted">选择要显示的关键词</small>
                                </div>

                                <!-- 公司筛选 -->
                                <div class="col-md-3">
                                    <label class="form-label">公司筛选</label>
                                    <select id="resultsCompanyFilter" class="form-select form-select-sm" multiple>
                                        <!-- 动态填充公司选项 -->
                                    </select>
                                    <small class="text-muted">选择要显示的公司</small>
                                </div>

                                <!-- 出现次数筛选 -->
                                <div class="col-md-3">
                                    <label class="form-label">出现次数筛选</label>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">≥</span>
                                        <input type="number" id="minOccurrenceCount" class="form-control"
                                               value="1" min="0" placeholder="最小次数">
                                    </div>
                                    <div class="form-check form-check-sm mt-1">
                                        <input class="form-check-input" type="checkbox" id="hideZeroOccurrences" checked>
                                        <label class="form-check-label" for="hideZeroOccurrences">
                                            隐藏0次记录
                                        </label>
                                    </div>
                                </div>

                                <!-- 筛选操作 -->
                                <div class="col-md-3">
                                    <label class="form-label">操作</label>
                                    <div class="d-grid gap-1">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="applyResultsFilters()">
                                            <i class="bi bi-check"></i> 应用筛选
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearResultsFilters()">
                                            <i class="bi bi-x"></i> 清除筛选
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 快速筛选按钮 -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label class="form-label small">快速筛选:</label>
                                    <div class="btn-group btn-group-sm me-2" role="group">
                                        <button type="button" class="btn btn-outline-info" onclick="quickFilter('hideZero')">
                                            隐藏0次
                                        </button>
                                        <button type="button" class="btn btn-outline-info" onclick="quickFilter('showAll')">
                                            显示全部
                                        </button>
                                    </div>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-warning" onclick="quickFilter('highCount')">
                                            高频词(≥5)
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" onclick="quickFilter('lowCount')">
                                            低频词(1-4)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 快捷键提示 -->
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="bi bi-keyboard"></i>
                                        快捷键: Ctrl+Enter 应用筛选 | Ctrl+R 清除筛选 | Esc 关闭面板
                                    </small>
                                </div>
                            </div>

                            <!-- 筛选状态显示 -->
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div id="resultsFilterStatus" class="text-muted small">
                                        <!-- 筛选状态信息 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div id="resultsContent">
                            <!-- 结果表格将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计摘要模态框 -->
    <div class="modal fade" id="summaryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-bar-chart"></i> 统计摘要</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="summaryContent">
                        <!-- 摘要内容将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI分析模态框 -->
    <div class="modal fade" id="aiAnalysisModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-robot"></i> AI分析
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 左右布局容器 -->
                    <div class="row">
                        <!-- 左侧：输入表单区域 -->
                        <div class="col-lg-5" id="aiInputPanel">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="bi bi-pencil-square"></i> 分析参数设置
                                    </h6>
                                </div>
                                <div class="card-body" style="max-height: 700px; overflow-y: auto;">
                                    <!-- AI分析输入区域 -->
                                    <div class="mb-3">
                                        <label for="aiStockCodes" class="form-label">
                                            <i class="bi bi-graph-up"></i> 股票代码
                                        </label>
                                        <textarea class="form-control" id="aiStockCodes" rows="3" placeholder="000001&#10;000002&#10;600000"></textarea>
                                        <small class="text-muted">每行一个股票代码</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="aiKeywords" class="form-label">
                                            <i class="bi bi-search"></i> 统计关键词
                                        </label>
                                        <textarea class="form-control" id="aiKeywords" rows="3" placeholder="协同创新&#10;技术合作&#10;研发合作"></textarea>
                                        <small class="text-muted">每行一个关键词</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="aiRelatedParties" class="form-label">
                                            <i class="bi bi-people"></i> 关联方 <span class="text-muted">(可选)</span>
                                        </label>
                                        <textarea class="form-control" id="aiRelatedParties" rows="3" placeholder="华为技术有限公司&#10;腾讯科技&#10;阿里巴巴"></textarea>
                                        <small class="text-muted">每行一个关联方名称，留空则分析所有找到的关联方</small>
                                    </div>

                                    <!-- AI分析提示词 -->
                                    <div class="mb-3">
                                        <label for="aiPrompt" class="form-label">
                                            <i class="bi bi-chat-dots"></i> AI分析要求
                                        </label>
                                        <textarea class="form-control" id="aiPrompt" rows="4" placeholder="根据搜索到的全部关联方公司的上下文内容，判断股票代码对应的公司和哪些关联方公司存在协同创新。"></textarea>
                                        <small class="text-muted">描述您希望AI如何分析这些内容</small>
                                    </div>

                                    <!-- 控制按钮 -->
                                    <div class="d-grid gap-2 mb-3">
                                        <button type="button" class="btn btn-primary" id="startAiAnalysisBtn">
                                            <i class="bi bi-play-circle"></i> 开始AI分析
                                        </button>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <button type="button" class="btn btn-outline-secondary w-100" id="clearAiFormBtn">
                                                    <i class="bi bi-arrow-clockwise"></i> 清空表单
                                                </button>
                                            </div>
                                            <div class="col-6">
                                                <button type="button" class="btn btn-outline-info w-100" id="showHistoryBtn" onclick="showAnalysisHistory()">
                                                    <i class="bi bi-clock-history"></i> 历史记录
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- AI配置区域 -->
                                    <div class="border rounded p-3 mb-3">
                                        <h6 class="mb-3">
                                            <i class="bi bi-gear"></i> AI配置
                                        </h6>

                                        <div class="mb-3">
                                            <label for="openaiModel" class="form-label">模型选择</label>
                                            <div class="input-group input-group-sm">
                                                <select class="form-select" id="openaiModel">
                                                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                                    <option value="gpt-4">GPT-4</option>
                                                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                                    <option value="gpt-4o">GPT-4o</option>
                                                    <option value="o3">O3</option>
                                                    <option value="claude-sonnet-4" selected>Claude Sonnet 4</option>
                                                </select>
                                                <button type="button" class="btn btn-outline-primary" id="getModelsBtn" onclick="getAvailableModels()">
                                                    <i class="bi bi-arrow-clockwise"></i> 获取模型
                                                </button>
                                            </div>
                                            <small class="text-muted">使用服务器配置的API，点击"获取模型"动态加载可用模型</small>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="enableThinking" checked>
                                                <label class="form-check-label" for="enableThinking">
                                                    <i class="bi bi-lightbulb"></i> 开启深度思考
                                                </label>
                                            </div>
                                            <small class="text-muted">启用后AI将进行更深入的分析思考（可能增加响应时间）</small>
                                        </div>

                                        <div class="d-flex gap-2 mb-2">
                                            <button type="button" class="btn btn-outline-success btn-sm flex-fill" onclick="testOpenAIConnection()">
                                                <i class="bi bi-check-circle"></i> 测试连接
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-sm flex-fill" onclick="testOpenAISimple()">
                                                <i class="bi bi-bug"></i> 调试API
                                            </button>
                                        </div>

                                        <div class="alert alert-info p-2">
                                            <small>
                                                <i class="bi bi-info-circle"></i>
                                                <strong>说明：</strong>使用服务器端配置的API Key和Base URL，无需手动配置
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：AI分析结果区域 -->
                        <div class="col-lg-7" id="aiResultsPanel">
                            <div class="card h-100">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-lightbulb"></i> AI分析结果
                                        </h6>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" onclick="toggleMarkdownView()">
                                                <i class="bi bi-markdown" id="markdownIcon"></i>
                                                <span id="markdownText">Markdown</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0" style="height: 700px;">
                                    <!-- 默认提示信息 -->
                                    <div id="aiAnalysisPlaceholder" class="d-flex align-items-center justify-content-center h-100 text-muted">
                                        <div class="text-center">
                                            <i class="bi bi-robot" style="font-size: 3rem; opacity: 0.3;"></i>
                                            <p class="mt-3 mb-0">请填写左侧参数并点击"开始AI分析"</p>
                                        </div>
                                    </div>

                                    <!-- AI分析结果容器 -->
                                    <div id="aiAnalysisResults" style="display: none; height: 100%; overflow-y: auto;">
                                        <!-- AI分析内容 -->
                                        <div class="p-3 border-bottom">
                                            <div id="aiStreamingContent" class="ai-content-display">
                                                <!-- 流式AI分析结果将在这里显示 -->
                                            </div>
                                            <div id="aiStreamingIndicator" style="display: none;">
                                                <div class="text-center py-3">
                                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <span class="text-muted">AI正在分析中...</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 折叠的上下文引用 -->
                                        <div class="border-top" id="contextSection" style="display: none;">
                                            <div class="p-3 bg-light border-bottom">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <button class="btn btn-link text-decoration-none p-0 flex-grow-1 text-start" type="button"
                                                            data-bs-toggle="collapse" data-bs-target="#contextReferences"
                                                            aria-expanded="false" aria-controls="contextReferences">
                                                        <i class="bi bi-chevron-right" id="contextToggleIcon"></i>
                                                        <i class="bi bi-quote"></i>
                                                        引用上下文 (<span id="contextCount">0</span>)
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" type="button" onclick="debugAiContextState()" title="调试上下文状态">
                                                        <i class="bi bi-bug"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="collapse" id="contextReferences">
                                                <div class="p-3" id="contextContent">
                                                    <!-- 搜索到的上下文将在这里显示 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <!-- 关联方分析分页弹窗 -->
    <div class="modal fade" id="relatedPartyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-people-fill"></i> 关联方协同创新分析
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 统计信息 -->
                    <div id="relatedPartyStats" class="mb-4">
                        <!-- 统计卡片将在这里显示 -->
                    </div>

                    <!-- 分页控制 -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">详细分析结果</h6>
                        <div class="d-flex align-items-center">
                            <label for="relatedPartyPageSize" class="form-label me-2 mb-0">每页显示:</label>
                            <select class="form-select form-select-sm me-3" id="relatedPartyPageSize" style="width: auto;" onchange="updateRelatedPartyPageSize()">
                                <option value="5">5条</option>
                                <option value="10" selected>10条</option>
                                <option value="20">20条</option>
                                <option value="50">50条</option>
                            </select>
                            <span id="relatedPartyPageInfo" class="text-muted small"></span>
                        </div>
                    </div>

                    <!-- 关联方分析内容 -->
                    <div id="relatedPartyContent">
                        <!-- 分页内容将在这里显示 -->
                    </div>

                    <!-- 分页导航 -->
                    <div id="relatedPartyPagination" class="d-flex justify-content-center mt-4">
                        <!-- 分页控件将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2" id="loadingText">处理中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI分析历史记录模态框 -->
    <div class="modal fade" id="analysisHistoryModal" tabindex="-1" aria-labelledby="analysisHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="analysisHistoryModalLabel">
                        <i class="bi bi-clock-history"></i> AI分析历史记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- 左侧：历史记录列表 -->
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-list"></i> 历史记录列表
                                        </h6>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearAnalysisHistory()">
                                            <i class="bi bi-trash"></i> 清空
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body p-0" style="max-height: 500px; overflow-y: auto;">
                                    <div id="historyList">
                                        <!-- 历史记录列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧：历史记录详情 -->
                        <div class="col-md-8">
                            <div class="card h-100">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-file-text"></i> 分析结果详情
                                        </h6>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" id="loadHistoryBtn" onclick="loadHistoryToMain()" disabled>
                                                <i class="bi bi-arrow-left"></i> 加载到主界面
                                            </button>
                                            <button type="button" class="btn btn-outline-info" id="toggleHistoryMarkdown" onclick="toggleHistoryMarkdownView()">
                                                <i class="bi bi-markdown"></i> Markdown
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                    <div id="historyDetail">
                                        <div class="text-center text-muted py-5">
                                            <i class="bi bi-arrow-left" style="font-size: 2rem; opacity: 0.3;"></i>
                                            <p class="mt-3 mb-0">请从左侧选择一个历史记录查看详情</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
